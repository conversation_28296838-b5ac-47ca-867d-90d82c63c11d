---
description: WOTA 專案指南
globs:
alwaysApply: false
---

# WOTA 專案指南

## 編碼規範

### 函數命名規則
- 私有函數在跨 package 呼叫時，必須改成開頭大寫
  - 例如：`getUserInfo()` 而非 `getuserinfo()`

### 指標（Pointer）使用規範
- 非必要請勿返回 pointer
- 如需使用 pointer 返回值，則必須處理 nil 情況，否則會造成程式 crash
  ```go
  // 錯誤示範
  user := getUserPointer()
  fmt.Println(user.Name) // 可能 panic
  
  // 正確示範
  user := getUserPointer()
  if user != nil {
      fmt.Println(user.Name)
  }
  ```

### 多返回值處理
- 函數返回多個值時，必須處理所有返回值
- 即使不使用某個返回值，也要用 `_` 來接收
  ```go
  // 錯誤示範
  result := doSomething() // 忽略 error
  
  // 正確示範
  result, err := doSomething()
  if err != nil {
      // 處理錯誤
  }
  
  // 或者明確忽略
  result, _ := doSomething()
  ```
