---
description: 
globs: 
alwaysApply: true
---
# Principles
- 任何不確定或是需求模糊的情況，都需與我再次確認或反問。
- 做比較複雜的需求時，須先進行 plan 跟我確認後再進行 act 實際的改動。
- 不在我明確指示下的範圍內，不要幫我擅自改動任何東西，除非他跟現在的需求有明確的關聯。
- 如果該專案已有其他內容，需參考其他內容的風格，維持整體專案的一致性。
- 程式風格沒參考對象時優先使用官方指南或社群 convention。
- 每次修正都要幫我留意修改的地方要用 linter 做 formatting。
- 禁止增刪改超過專案範圍內的資料，只允許讀。
- 在 llm foundation model 原始訓練資料不確定或可能不是最新的事情可以上網查詢再繼續處理。
- Before processing a user's request, you must use the "list_all_tools" command to identify which tools are available.- 
- 操作結束前幫我運行 go fmt 整理格式