# Wota Ship Crawler

一個專門用於爬取 Regent Seven Seas Cruises（麗晶七海郵輪）官方網站資料的 Go 微服務。

## 概述

本專案自動化地從 [https://www.rssc.com/](https://www.rssc.com/) 抓取郵輪相關資訊，並將資料結構化存儲。支援非同步處理和 callback 機制，適合整合到更大的系統架構中。

## 功能特點

- **非同步處理**：所有爬蟲任務都在背景執行，API 立即返回響應
- **任務追蹤**：資料庫記錄每個爬蟲任務的執行狀態
- **Callback 機制**：任務完成後自動通知指定的 callback URL
- **媒體處理**：自動下載圖片和影片並上傳到 AWS S3
- **錯誤處理**：完善的錯誤追蹤和重試機制

## 支援的資料類型

### 支援的郵輪
- Seven Seas Grandeur
- Seven Seas Splendor
- Seven Seas Explorer
- Seven Seas Voyager
- Seven Seas Mariner
- Seven Seas Navigator

### 可爬取的資料
- **船隻規格 (Specs)**：乘客數、船員數、套房數、甲板數、長度、寬度、噸位
- **甲板圖片 (Deck Images)**：各層甲板的平面圖
- **船隻影片 (Videos)**：YouTube 介紹影片
- **套房資料 (Suites)**：各類型套房的詳細資訊
- **航線資料 (Cruises)**：包含詳細行程和地圖

## API 端點

### 健康檢查
- `GET /v1/ping` - 服務健康檢查
- `GET /v1/version` - 取得版本資訊
- `GET /v1/secret` - 檢查 AWS SSM 配置狀態

### 爬蟲 API
- `POST /v1/crawler/regent-seven-seas/specs` - 爬取船隻規格
- `POST /v1/crawler/regent-seven-seas/deck-images` - 爬取甲板圖片
- `POST /v1/crawler/regent-seven-seas/videos` - 爬取船隻影片
- `POST /v1/crawler/regent-seven-seas/suites` - 爬取套房資料
- `POST /v1/crawler/regent-seven-seas/cruises` - 爬取航線資料

### API 使用範例

```bash
curl -X POST http://localhost:8080/v1/crawler/regent-seven-seas/specs \
  -H "Content-Type: application/json" \
  -d '{
    "ship_name": "Seven Seas Explorer",
    "callback_url": "https://your-callback-url.com/webhook"
  }'
```

## 技術架構

### 核心技術
- **語言**：Go
- **Web 框架**：[Gin](https://github.com/gin-gonic/gin)
- **ORM**：[GORM](https://gorm.io/) with MySQL
- **Web 爬蟲**：[go-rod](https://github.com/go-rod/rod)（瀏覽器自動化）
- **快取**：Redis
- **雲端服務**：AWS (S3, SSM)
- **容器化**：Docker

### 架構設計
- Clean Architecture 設計模式
- 中介軟體鏈：CORS → Language → Platform → Access Log → Parse Token → Auth Required → Permission
- 非同步任務處理與狀態追蹤
- 配置優先級：環境變數 > AWS SSM > 配置檔案 > 預設值

## 快速開始

### 前置需求
- Go 1.21+
- MySQL 8.0+
- Redis 6.0+
- Docker（選用）

### 本地開發

1. 複製專案並初始化
```bash
git clone <repository-url>
cd wota_ship_crawler
make init  # 初始化 Go modules 和私有倉庫訪問
```

2. 設定環境變數
```bash
cp conf/config.env.example conf/config.env
# 編輯 conf/config.env 填入必要配置
```

3. 生成 API 文檔
```bash
make swag
```

4. 編譯並運行
```bash
make build
make run
```

### Docker 部署

```bash
# 構建映像
docker build -t wota-ship-crawler .

# 運行容器
docker run -d \
  --name wota-ship-crawler \
  -p 8080:8080 \
  --env-file conf/config.env \
  wota-ship-crawler
```

## 環境配置

### 必要配置
| 變數名 | 說明 | 範例 |
|--------|------|------|
| ENV | 環境標識 | DEV/UAT/PROD |
| MAIN_ORG | 組織標識 | wota/bdt |
| BE_DSN | MySQL 連接字串 | user:pass@tcp(host:3306)/db |
| REDIS_HOST | Redis 主機 | localhost:6379 |
| PORT | 服務端口 | 8080 |

### AWS 配置
| 變數名 | 說明 |
|--------|------|
| S3_ACCESS_ID | S3 存取 ID |
| S3_ACCESS_KEY | S3 存取金鑰 |
| S3_REGION | S3 區域 |
| S3_BUCKET | S3 儲存桶名稱 |
| USE_SSM | 是否使用 AWS SSM |

完整的配置項目請參考 `conf/config.env.example`

## 開發指南

### 新增爬蟲功能

1. 在 `/internal/service/` 建立新的爬蟲服務檔案
2. 建立對應的測試檔案 `*_test.go`
3. 在 `/internal/dto/` 定義請求和響應結構
4. 在 `/internal/controller/crawler.go` 新增控制器方法
5. 在 `/internal/router/router.go` 註冊路由
6. 執行 `make swag` 更新 API 文檔

### 執行測試

```bash
# 執行特定服務的測試
go test ./internal/service/crawl_regent_seven_seas_cruises_test.go

# 測試模式：go test ./internal/service/<service_name>_test.go
```

## 部署

### CI/CD
專案使用 Drone CI 進行自動化部署：

```bash
make deploy  # 互動式選擇部署環境（UAT/PROD）
```

### 監控與日誌
- 使用 Zerolog 進行結構化日誌記錄
- 整合 Loki 進行日誌收集和分析
- 每個爬蟲任務都有唯一的追蹤 ID

## License

This project is proprietary software. All rights reserved.