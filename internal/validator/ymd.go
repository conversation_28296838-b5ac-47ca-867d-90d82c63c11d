package validator

import (
	"github.com/go-playground/validator/v10"
	"time"
)

var ymd validator.Func = func(fl validator.FieldLevel) bool {
	data, ok := fl.Field().Interface().(string)
	if ok {
		if data == "" {
			return true
		}
		if _, err := time.Parse("2006-01-02", data); err != nil {
			return false
		}
	}
	return true
}

//func ymd(
//	v *validator.Validate, topStruct reflect.Value, currentStructOrField reflect.Value,
//	field reflect.Value, fieldType reflect.Type, fieldKind reflect.Kind, param string,
//) bool {
//	fmt.Println("topStruct", topStruct)
//	fmt.Println("param", param)
//	fmt.Println("fieldKind", fieldKind)
//	fmt.Println("fieldType", fieldType)
//	fmt.Println("currentStructOrField", currentStructOrField)
//
//	if data, ok := field.Interface().(string); ok {
//		if _, err := time.Parse("2006-01-02", data); err != nil {
//			return false
//		}
//	}
//	return true
//}
