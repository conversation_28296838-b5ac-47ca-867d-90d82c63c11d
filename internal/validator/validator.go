package validator

import (
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

func InitValidator() {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		v.RegisterValidation("order_id", orderId)
		v.RegisterValidation("password", password)
		v.RegisterValidation("ymd", ymd)
		v.RegisterValidation("contains_if", containsIf)
		v.RegisterValidation("taxNumber", taxNumber)
	}
}
