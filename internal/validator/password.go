package validator

import (
	"github.com/go-playground/validator/v10"
	"unicode"
)

var password validator.Func = func(fl validator.FieldLevel) bool {
	data, ok := fl.Field().Interface().(string)
	if ok {
		if data == "" {
			return true
		}
		var (
			hasLen    = false
			hasUpper  = false
			hasLower  = false
			hasNumber = false
			//hasSpecial = false
		)
		if len(data) >= 6 && len(data) <= 15 {
			hasLen = true
		}
		for _, char := range data {
			switch {
			case unicode.IsUpper(char):
				hasUpper = true
			case unicode.IsLower(char):
				hasLower = true
			case unicode.IsNumber(char):
				hasNumber = true
				//case unicode.IsPunct(char) || unicode.IsSymbol(char):
				//	hasSpecial = true
			}
		}
		return hasLen && hasUpper && hasLower && hasNumber
	}
	return true // 沒有值，就給通過
}
