package validator

import (
	"github.com/go-playground/validator/v10"
	"reflect"
	"regexp"
)

var orderId validator.Func = func(fl validator.FieldLevel) bool {
	data, ok := fl.Field().Interface().(string)
	if ok {
		regex := regexp.MustCompile(`^[A-Z]{1}\d{5}[A-Z]{2}\d*$`)
		if !regex.MatchString(data) {
			return false
		}
	}
	return true
}

func NameValid(
	v *validator.Validate, topStruct reflect.Value, currentStructOrField reflect.Value,
	field reflect.Value, fieldType reflect.Type, fieldKind reflect.Kind, param string,
) bool {
	if s, ok := field.Interface().(string); ok {
		if s == "admin" {
			return false
		}
	}
	return true
}
