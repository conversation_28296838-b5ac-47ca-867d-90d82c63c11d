package schedule

import (
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/env"
	"github.com/robfig/cron/v3"
)

var config = conf.Config

func Admin() {
	if config.ENV == env.LOCAL {
		return
	}

	go func() {
		// 每日 0點 10分 執行
		spec := "10 0 * * *"
		// fmt.Println("Cron: Remove Disuse Media Set " + spec)
		loc, _ := time.LoadLocation(config.Timezone)
		c := cron.New(cron.WithLocation(loc))
		c.AddFunc(spec, func() {
			// service.BatchRemoveDisuseMedia()
		})
		c.Start()
	}()
}
