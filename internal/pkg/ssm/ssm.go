package ssm

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/secretsmanager"
	"github.com/mitchellh/mapstructure"
	"golang.org/x/exp/maps"
)

// GetAWSSecrets [keyPrefix] 優先使用前綴詞的 value，覆蓋其他無 prefix，可作為環境切分使用
// [secretId] 可以是 secretName 或 secretArn
func GetAWSSecrets(config interface{}, iamId string, iamSecret string, region string, secretId string, keyPrefix string) (interface{}, error) {

	if iamId == "" || iamSecret == "" || region == "" || secretId == "" {
		return nil, fmt.Errorf("lose ssm config")
	}

	awsSession, err := session.NewSession(&aws.Config{
		Region:      aws.String(region),
		Credentials: credentials.NewStaticCredentials(iamId, iamSecret, ""),
	})
	if err != nil {
		return nil, fmt.Errorf("NewSession error %s", err.Error())
	}

	result, err := secretsmanager.New(awsSession).GetSecretValue(&secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretId),
	})
	if err != nil {
		return nil, fmt.Errorf("GetSecretValue error %s", err.Error())
	}
	if result.SecretString == nil {
		return nil, fmt.Errorf("no secret in %s", secretId)
	}

	var jsonObj map[string]interface{}
	if err := json.Unmarshal([]byte(*result.SecretString), &jsonObj); err != nil {
		return nil, fmt.Errorf("json.Unmarshal error %s", err.Error())
	}
	if keyPrefix != "" {
		includes := make(map[string]interface{})
		excludes := make(map[string]interface{})

		for k, v := range jsonObj {
			if strings.HasPrefix(k, keyPrefix) {
				includes[k[len(keyPrefix):]] = v
			} else {
				excludes[k] = v
			}
		}
		// includes override excludes
		maps.Copy(excludes, includes)
		jsonObj = excludes
	}

	mapstructure.WeakDecode(jsonObj, &config)

	return config, nil
}
