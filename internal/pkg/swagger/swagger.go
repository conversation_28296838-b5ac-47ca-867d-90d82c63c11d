package swagger

import (
	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/env"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/middleware"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"github.com/swaggo/swag"
)

var config = conf.Config

func InitSwagger(r *gin.Engine, swaggerFilePath string, swaggerInfo *swag.Spec) error {
	// Override Version
	if config.BuildVersion != "" {
		config.Version = "build " + config.BuildVersion
		if tool.InArray(config.ENV, []string{env.UAT, env.PROD}) {
			config.Version = config.BuildVersion
		}
	}
	swaggerInfo.Version = config.Version

	// Protocol
	swaggerInfo.Schemes = []string{"http", "https"}
	if tool.InArray(config.ENV, []string{env.DEV, env.UAT, env.PROD}) {
		gin.SetMode(gin.ReleaseMode)
		swaggerInfo.Schemes = []string{"https"}
	}

	//r.StaticFile("doc.json", swaggerFilePath)
	//url := ginSwagger.URL("doc.json")

	rg := r.Group("")
	{
		rg.Use(middleware.Swagger(swaggerInfo))
		rg.GET("swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler, ginSwagger.URL("doc.json")))
	}

	return nil
}
