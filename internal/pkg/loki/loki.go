package loki

import (
	"fmt"
	"log"
	"reflect"
	"regexp"
	"runtime"
	"strings"
	"time"

	"encoding/json"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"github.com/fluent/fluent-logger-golang/fluent"
)

var fromInside = false
var fluentBitHost string
var fluentBitPort int
var osEnv string

const logMaxLength = 2000

func ToStringMap(data interface{}) (map[string]interface{}, string) {
	m := make(map[string]interface{})
	parse, err := json.Marshal(data)
	if err != nil {
		fmt.Println(err)
		return nil, ""
	}
	json.Unmarshal(parse, &m)
	return m, string(parse)
}

func GetCurrentFuncName(skip int) string {
	// current = 1, second = 2
	pc, _, _, _ := runtime.Caller(skip)
	return fmt.Sprintf("%s", runtime.FuncForPC(pc).Name())
}
func getCurrentFuncName() string {
	return GetCurrentFuncName(3)
}

func FormatValidation(errMsg string) []string {
	var msgs []string
	for _, row := range strings.Split(errMsg, "\n") {
		var matches []string
		var patten = regexp.MustCompile(`(?mi)Key: '([^']*)' Error:Field validation for '([^']*)' failed on the '(.*)' tag`)
		matches = patten.FindStringSubmatch(row)
		if len(matches) >= 3 {
			str := "欄位 " + matches[2] + " "
			switch matches[3] {
			case "len":
				str += "長度有誤"
			default:
				str += "格式有誤"
			}
			msgs = append(msgs, str)
		} else {
			msgs = append(msgs, row)
		}

	}
	return msgs
}

func InitFluentBit(host string, port int, env string) {
	fluentBitHost = host
	fluentBitPort = port
	osEnv = env
}

func FluentBit(tag string, data interface{}) {
	if fluentBitHost == "" || fluentBitPort == 0 || osEnv == "" {
		log.Println("Error: you need init fluentBit first: ", fluentBitHost, fluentBitPort, osEnv)

		stdoutString := tool.ToJson(data)
		if len(stdoutString) > logMaxLength {
			stdoutString = stdoutString[0:logMaxLength] + "..."
		}
		log.Printf(stdoutString)
		return
	}

	st := time.Now().Sub(time.Unix(0, 0)).Seconds()
	logger, linkErr := fluent.New(fluent.Config{
		FluentHost:   fluentBitHost,
		FluentPort:   fluentBitPort,
		Async:        true,
		MaxRetry:     2,
		MaxRetryWait: 10000,
		AsyncResultCallback: func(byteData []byte, err error) {
			if err != nil {
				log.Printf("[Error] " + err.Error())
				stdoutString := tool.ToJson(data)
				if len(stdoutString) > logMaxLength {
					stdoutString = stdoutString[0:logMaxLength] + "..."
				}
				log.Printf(stdoutString)
			}
			log.Printf("fluentbit spend time: %f", time.Now().Sub(time.Unix(0, 0)).Seconds()-st)
		},
	})
	if linkErr != nil {
		log.Printf("[Error] " + linkErr.Error())
	}
	defer logger.Close()

	postErr := logger.Post(tag, data)
	if postErr != nil {
		log.Printf("[Error] " + postErr.Error())
	}
}

const (
	DEBUG   = "debug"
	INFO    = "info"
	WARNING = "warning"
	ERROR   = "error"
)

type LokiLog struct {
	Level    string      `json:"level"`
	Topic    string      `json:"topic,omitempty"`
	Message  string      `json:"message"`
	Subtitle string      `json:"subtitle,omitempty"`
	FilePath string      `json:"filePath"`
	Data     interface{} `json:"data,omitempty"`
	TraceId  string      `json:"traceId"`
}

func Loki(lokiLog LokiLog) {

	if osEnv == "PROD" && lokiLog.Level != ERROR {
		//return
	}

	lokiLog.FilePath = getCurrentFuncName()

	lokiData := map[string]interface{}{
		"env":      osEnv,
		"level":    lokiLog.Level,
		"topic":    lokiLog.Topic,
		"subtitle": lokiLog.Subtitle,
		"message":  lokiLog.Message,
		"traceId":  lokiLog.TraceId,
		"filepath": lokiLog.FilePath,
	}

	if lokiLog.Data != nil && reflect.TypeOf(lokiLog.Data).Kind() == reflect.String {
		lokiData["Data"] = lokiLog.Data
	} else {
		lokiData["Data"], _ = ToStringMap(lokiLog.Data)
	}

	//logMaxLength := 2000
	//if len(dataJson) > logMaxLength {
	//	if osEnv == "LOCAL" {
	//		log.Println("[LOKI]", fmt.Sprintf("LOG IS TOO LONG, LIMIT %d !", logMaxLength), dataJson)
	//	}
	//	lokiData["Data"] = dataJson[0:logMaxLength] + "..."
	//}
	//
	//if osEnv == "LOCAL" {
	//	fmt.Println("[LOKI-" + lokiLog.Level + "]", lokiLog.FilePath, lokiLog.Topic, lokiLog.Subtitle, lokiLog.Message, ToJson(lokiLog.Data))
	//	return
	//}

	fromInside = true

	go FluentBit("program_log", lokiData)

	//go Loki("__program_log__", LokiLog{
	//	Level: level,
	//	Message: message,
	//	Data: datas,
	//})
}

func Log(lokiLog LokiLog) {

	if osEnv == "PROD" && lokiLog.Level == DEBUG {
		return
	}

	lokiLog.FilePath = getCurrentFuncName()

	lokiData := map[string]interface{}{
		"env":      osEnv,
		"level":    lokiLog.Level,
		"topic":    lokiLog.Topic,
		"subtitle": lokiLog.Subtitle,
		"message":  lokiLog.Message,
		"traceId":  lokiLog.TraceId,
		"filepath": lokiLog.FilePath,
	}
	dataJson := ""
	if lokiLog.Data != nil && reflect.TypeOf(lokiLog.Data).Kind() == reflect.String {
		lokiData["Data"] = lokiLog.Data
	} else {
		lokiData["Data"], dataJson = ToStringMap(lokiLog.Data)
	}

	if len(dataJson) > logMaxLength {
		if osEnv == "LOCAL" {
			log.Println("[LOKI]", fmt.Sprintf("LOG IS TOO LONG, LIMIT %d !", logMaxLength), dataJson)
		}
		lokiData["Data"] = dataJson[0:logMaxLength] + "..."
	}

	fromInside = true

	go FluentBit("program_log", lokiData)
}
