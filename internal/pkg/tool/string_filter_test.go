package tool

import (
	"testing"
)

func TestFilterAlphabetsAndSpace(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{"only letters and spaces", "Hello, World!", "Hello World"},
		{"includes numbers", "1234 Hello, World!", " Hello World"},
		{"includes special characters", "*&^%$#@! Hello, World!", " Hello World"},
		{"empty string", "", ""},
		{"no filter needed", "Hello World", "Hello World"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := StringFilterAlphabetsAndSpace(tt.input); got != tt.expected {
				t.<PERSON>("FilterAlphabetsAndSpace() = %v, want %v", got, tt.expected)
			}
		})
	}
}
