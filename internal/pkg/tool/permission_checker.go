package tool

import (
	"strings"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/claim"
	"github.com/gin-gonic/gin"
	"golang.org/x/exp/slices"
)

// CheckRole 檢查是否擁有此角色
func CheckRole(c *gin.Context, role string) bool {
	return InArray(role, c.Get<PERSON>lice(claim.Roles))
}

// CheckRoleContains 檢查是否擁有此角色(部分符合)
func CheckRoleContains(c *gin.Context, role string) bool {
	return slices.ContainsFunc(c.GetStringSlice(claim.Roles), func(val string) bool {
		return strings.Contains(val, role)
	})
}

// CheckPermission 檢查是否擁有此權限
func CheckPermission(c *gin.Context, permission string) bool {
	return InArray(permission, c.GetStringSlice(claim.Permissions))
}
