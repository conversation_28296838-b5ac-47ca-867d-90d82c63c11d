package tool

import (
	"bytes"
	"fmt"
	"golang.org/x/net/html"
	"strings"
	"testing"
)

var htmlStr = `
<!DOCTYPE html>
<html>
    <head>
        <script type="text/javascript" async="" src=""></script>
        <script src=""></script>
        <script type="text/javascript" async="" src=""></script>
        <script type="text/javascript" async="" src=""></script>
        <script src="" async=""></script>
        <script async="" src="">alert('hahaha')</script>
        <script type="text/javascript" async="" src=""></script>
        <script async="" src=""></script>
        <javascript>alert('hahaha')</javascript>
        <link rel="stylesheet" media="screen" href=""/>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
        <style type="text/css">.at-icon{fill:#fff;border:0}.at-icon-wrapper{display:inline-block;overflow:hidden}</style></head><body>
        <a href="/hotel?code=javascript:alert('haha')">click me!</a>
		<a click="javascript:alert('haha\"\>')" style="color:red">click me!</a>
	</body></html>`

func TestHtmlRemoveTags(t *testing.T) {
	doc, err := html.Parse(strings.NewReader(htmlStr))
	if err != nil {
		t.Error(err)
		return
	}
	var content bytes.Buffer
	HtmlRemoveTags(doc, []string{"javascript", "script"})
	html.Render(&content, doc)
	fmt.Printf("%s\n", content.Bytes())
}

func TestHtmlRemoveTagsByRegular(t *testing.T) {
	res := HtmlRemoveTagsByRegular(htmlStr, []string{"javascript", "script"})
	fmt.Printf("%s\n", res)
}

func TestHtmlRemoveAJavascriptByRegular(t *testing.T) {
	res := HtmlRemoveAJavascriptByRegular(htmlStr)
	fmt.Printf("%s\n", res)
}

func TestHtmlCleanInjection(t *testing.T) {
	res := HtmlCleanInjection(htmlStr)
	fmt.Printf("%s\n", res)
}
