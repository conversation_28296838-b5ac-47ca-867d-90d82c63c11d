package tool

import (
	"encoding/json"
	"io"
	"log"
	"net/http"
)

func CallApiGetWithHeader(url string, token string, org string, app string, res interface{}) error {
	// build request
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return err
	}

	req.Header.Add("Org", org)
	req.Header.Add("App", app)
	if token != "" {
		req.Header.Add("Authorization", token)
	}

	// send request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer Close(resp.Body)

	raw, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(raw, &res); err != nil {
		return err
	}
	return nil
}

func CallApiPostWithHeader(url string, token string, org string, app string, contentType string, body io.Reader, res interface{}) error {
	// build request
	req, err := http.NewRequest(http.MethodPost, url, body)
	if err != nil {
		return err
	}

	req.Header.Add("Org", org)
	req.Header.Add("App", app)
	if token != "" {
		req.Header.Add("Authorization", token)
	}
	if contentType != "" {
		req.Header.Add("Content-Type", contentType)
	}

	// send request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer Close(resp.Body)

	raw, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(raw, &res); err != nil {
		return err
	}

	return nil
}

func CallApiDeleteWithHeader(url string, token string, org string, app string, contentType string, body io.Reader, res interface{}) error {
	// build request
	req, err := http.NewRequest(http.MethodDelete, url, body)
	if err != nil {
		return err
	}

	if token != "" {
		req.Header.Add("Authorization", token)
		req.Header.Add("Org", org)
		req.Header.Add("App", app)
	}
	if contentType != "" {
		req.Header.Add("Content-Type", contentType)
	}

	// send request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer Close(resp.Body)

	raw, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(raw, &res); err != nil {
		return err
	}

	return nil
}

func Close(v io.Closer) {
	if err := v.Close(); err != nil {
		log.Println(err)
	}
}
