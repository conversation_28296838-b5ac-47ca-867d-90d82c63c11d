package tool

import (
	"os"
	"path/filepath"
	"testing"
)

func TestConvertPDFToImages(t *testing.T) {
	// This test requires Ghostscript to be installed
	// Skip if running in CI or if gs is not available
	if os.Getenv("CI") != "" {
		t.Skip("Skipping PDF conversion test in CI environment")
	}

	tempDir := filepath.Join(os.TempDir(), "pdf_converter_test")
	defer os.RemoveAll(tempDir)

	outputDir := filepath.Join(tempDir, "output")
	_, err := ConvertPDFToImages("non_existent.pdf", outputDir)
	if err == nil {
		t.Error("Expected error for non-existent PDF file, but got nil")
	}

	// Test output directory creation
	err = os.MkdirAll(outputDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create output directory: %v", err)
	}

	// Verify directory was created
	if _, err := os.Stat(outputDir); os.IsNotExist(err) {
		t.Error("Output directory was not created")
	}
}
