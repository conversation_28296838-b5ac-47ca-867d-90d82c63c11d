package tool

import (
	"testing"
)

func TestNumberFormatThousandth(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{"below thousand", "123", "123"},
		{"exactly thousand", "1000", "1,000"},
		{"large number", "123456789", "123,456,789"},
		{"with leading zeros", "0001234", "1,234"},
		// {"with decimal", "1234.5678", "1,234"},
		// {"empty string", "", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NumberFormatThousandth(tt.input); got != tt.expected {
				t.Errorf("NumberFormatThousandth(%v) = %v, want %v", tt.input, got, tt.expected)
			}
		})
	}
}
