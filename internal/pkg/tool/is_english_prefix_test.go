package tool

import "testing"

func TestIsEnglishPrefix(t *testing.T) {
	testCases := []struct {
		input    string
		expected bool
	}{
		{"Hello", true},
		{"hello", true},
		{"123abc", false},
		{"", false},
		{"!@#$%", false},
		{"A1B2", true},
		{"a1b2", true},
		{" Hello", false},
		{"Hello ", true},
	}

	for _, tc := range testCases {
		result := IsEnglishPrefix(tc.input)
		if result != tc.expected {
			t.Errorf("isEnglishPrefix(%q) = %v; expected %v", tc.input, result, tc.expected)
		}
	}
}
