package tool

import (
	"fmt"
	"strconv"
)

// this verification is for Taiwanese company
// based on https://www.fia.gov.tw/singlehtml/3?cntId=c4d9cff38c8642ef8872774ee9987283
func TaxNumberVerify(taxNumber string) bool {
	if len(taxNumber) != 8 {
		fmt.Println("tax number error: must be 8 digits")
		return false
	}

	// 將統編的每個數位拆分並轉換為整數
	digits := make([]int, 8)
	for i := 0; i < 8; i++ {
		digit, err := strconv.Atoi(string(taxNumber[i]))
		if err != nil || digit < 0 || digit > 9 {
			fmt.Println("tax number error: must be 8 digits")
			return false
		}
		digits[i] = digit
	}

	// 定義倍數
	multipliers := []int{1, 2, 1, 2, 1, 2, 4, 1}
	sum := 0

	// 計算總和
	for i := 0; i < 8; i++ {
		result := digits[i] * multipliers[i]
		if result > 9 {
			// 如果乘積是兩位數，將兩位數的數字相加
			sum += (result / 10) + (result % 10)
		} else {
			sum += result
		}
	}

	if sum%10 == 0 {
		return true
	} else if digits[6] == 7 && (sum+1)%10 == 0 {
		return true
	} else {
		return false
	}
}
