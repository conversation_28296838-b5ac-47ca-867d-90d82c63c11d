package tool

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// ConvertPDFToImages converts a PDF file to JPEG images using Ghostscript
// pdfPath: path to the input PDF file
// outputDir: directory where the converted images will be saved
// Returns: slice of paths to the generated image files
func ConvertPDFToImages(pdfPath string, outputDir string) ([]string, error) {
	err := os.MkdirAll(outputDir, 0755)
	if err != nil {
		return nil, fmt.Errorf("failed to create output directory: %v", err)
	}

	outputPattern := filepath.Join(outputDir, "page_%03d.jpg")

	// Use Ghostscript to convert PDF to images
	cmd := exec.Command("gs",
		"-dNOPAUSE",
		"-dBATCH",
		"-sDEVICE=jpeg",
		"-r300",
		"-dJPEGQ=95",
		fmt.Sprintf("-sOutputFile=%s", outputPattern),
		pdfPath)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("ghostscript failed: %v, output: %s", err, string(output))
	}

	// Find all generated image files
	imageFiles := []string{}
	err = filepath.Walk(outputDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && strings.HasSuffix(strings.ToLower(path), ".jpg") {
			imageFiles = append(imageFiles, path)
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to list generated image files: %v", err)
	}

	return imageFiles, nil
}
