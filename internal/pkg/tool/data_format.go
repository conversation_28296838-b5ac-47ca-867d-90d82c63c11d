package tool

import (
	"strings"
)

func FormatPhoneCode(phoneCountryCode string) string {
	if len(phoneCountryCode) > 0 && !strings.Contains(phoneCountryCode, "+") {
		phoneCountryCode = "+" + phoneCountryCode
	}

	return phoneCountryCode
}

func CreditCardHide(cardNumber string) string {
	starLen := len(cardNumber) - 8
	if starLen < 0 {
		starLen = 0
	}
	if len(cardNumber) >= 8 {
		return cardNumber[0:4] + strings.Repeat("*", starLen) + cardNumber[len(cardNumber)-4:]
	}

	return cardNumber
}
