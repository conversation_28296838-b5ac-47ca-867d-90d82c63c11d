package tool

import (
	"context"
	"fmt"
	"gorm.io/gorm/logger"
	"strings"
	"time"
)

type GormRecorder struct {
	logger.Interface
	Statements []string
}

// Trace 自定義 logger 給 migrate session 使用
func (r *GormRecorder) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	sql, _ := fc()
	keywords := []string{
		"select database()",
		"information_schema",
		"select * from `",
	}
	skip := false
	for _, keyword := range keywords {
		if strings.Contains(strings.ToLower(sql), keyword) {
			skip = true
			break
		}
	}
	if !skip {
		fmt.Println(sql, ";")
	}
	r.Statements = append(r.Statements, sql)
}
