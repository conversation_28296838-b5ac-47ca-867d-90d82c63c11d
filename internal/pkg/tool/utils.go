package tool

import (
	"encoding/json"
	"fmt"
	"reflect"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/exp/slices"
	"golang.org/x/net/html"

	"github.com/araddon/dateparse"
)

func SliceContains(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

func contains(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

func StringArrayUnique(e []string) []string {
	var r []string

	for _, s := range e {
		if !contains(r[:], s) {
			r = append(r, s)
		}
	}
	return r
}

func CaseInsensitiveReplace(subject string, search string, replace string) string {
	searchRegex := regexp.MustCompile("(?i)" + search)
	return searchRegex.ReplaceAllString(subject, replace)
}

func CaseInsensitiveContains(subject string, search string) bool {
	return strings.Contains(strings.To<PERSON>ower(subject), strings.ToLower(search))
}

func AddDays(date string, n int) (string, error) {
	const shortForm = "2006-01-02"
	t, err := time.Parse(shortForm, date)
	if err != nil {
		return "", err
	}
	m, err := time.ParseDuration(fmt.Sprintf("%dh", n*24))
	if err != nil {
		return "", err
	}
	m1 := t.Add(m)

	return m1.Format("2006-01-02"), nil
}

// AddTimes time units are "ns", "us" (or "µs"), "ms", "s", "m", "h".
func AddTimes(stringDate string, n int, unit string, useTimeZone bool) (string, error) {
	if useTimeZone != true {
		stringDate = strings.Split(stringDate, "+")[0]
	}

	t, _ := dateparse.ParseStrict(stringDate)

	m, err := time.ParseDuration(fmt.Sprintf("%d%s", n, unit))
	if err != nil {
		return "", err
	}
	m1 := t.Add(m)

	return m1.Format("2006-01-02 15:04"), nil
}

func DiffDays(beforeDate string, afterDate string) (int, error) {
	const shortForm = "2006-01-02"
	t1, err := time.Parse(shortForm, beforeDate)
	if err != nil {
		return 0, err
	}
	t2, err := time.Parse(shortForm, afterDate)
	if err != nil {
		return 0, err
	}
	return int(t2.Sub(t1).Hours() / 24), nil
}

func IsDateStringInRange(dateStr, startDateStr, endDateStr string) (bool, error) {
	layout := "2006-01-02"
	date, err := time.Parse(layout, dateStr)
	if err != nil {
		return false, err
	}

	startDate, err := time.Parse(layout, startDateStr)
	if err != nil {
		return false, err
	}

	endDate, err := time.Parse(layout, endDateStr)
	if err != nil {
		return false, err
	}

	return !date.Before(startDate) && !date.After(endDate), nil
}

func InArray(need interface{}, array interface{}) bool {
	switch key := need.(type) {
	case int:
		for _, item := range array.([]int) {
			if item == key {
				return true
			}
		}
	case string:
		for _, item := range array.([]string) {
			if item == key {
				return true
			}
		}
	case uint:
		for _, item := range array.([]uint) {
			if item == key {
				return true
			}
		}
	case int64:
		for _, item := range array.([]int64) {
			if item == key {
				return true
			}
		}
	case float64:
		for _, item := range array.([]float64) {
			if item == key {
				return true
			}
		}
	default:
		return false
	}
	return false
}

func IsSlice(v interface{}) bool {
	return reflect.TypeOf(v).Kind() == reflect.Slice
}

func GetDateStringInDays(days int) string {
	currentTime := time.Now()

	target := currentTime.AddDate(0, 0, days)
	return target.Format("2006-01-02")
}

func MapStringKeys(array map[string]interface{}) []string {
	var keys []string
	for key := range array {
		keys = append(keys, key)
	}
	return keys
}

func ToJson(data interface{}) string {
	parse, err := json.Marshal(data)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	return string(parse)
}

func JoinName(firstName string, lastName string) string {
	// 判斷是否為 a-z

	// 判斷中文長短，調整順序

	return fmt.Sprintf("%s %s", firstName, lastName)
}

func RegularLangCode(from string) (string, error) {
	if len(from) == 5 && strings.Contains(from, "-") {
		arr := strings.Split(from, "-")
		if len(arr) != 2 {
			return "", fmt.Errorf("split failed")
		}

		return fmt.Sprintf("%s-%s", strings.ToLower(arr[0]), strings.ToUpper(arr[1])), nil
	}

	return "", fmt.Errorf("no patten match")
}

type Pair struct {
	Key   string
	Value int
}

func SortMapStringInt(arr map[string]int, asc bool) []Pair {
	var res []Pair
	for s, i := range arr {
		res = append(res, Pair{
			Key:   s,
			Value: i,
		})
	}

	//p is sorted
	sort.Slice(res, func(i, j int) bool {
		if asc {
			return res[i].Value < res[j].Value // ASC
		} else {
			return res[i].Value > res[j].Value // DESC
		}
	})

	// do not use map, map not linear
	return res
}

func ArrayChunks[T any](items []T, chunkSize int) (chunks [][]T) {
	for chunkSize < len(items) {
		items, chunks = items[chunkSize:], append(chunks, items[0:chunkSize:chunkSize])
	}
	return append(chunks, items)
}

func UrlAddSlash(baseUrl string) string {
	if baseUrl[len(baseUrl)-1:] != "/" {
		baseUrl = baseUrl + "/"
	}
	return baseUrl
}

func FormatLangCode(lang string) string {
	arr := strings.Split(lang, "-")

	if len(arr) == 2 {
		return fmt.Sprintf("%s-%s", strings.ToLower(arr[0]), strings.ToUpper(arr[1]))
	}

	return strings.ToLower(lang)
}

func SliceDeleteByValue(s []string, v string) []string {
	pos := slices.Index(s, v)
	if pos > -1 {
		return slices.Delete(s, slices.Index(s, v), slices.Index(s, v)+1)
	}
	return nil
}

func DiffSliceBaseOn(newSlice, baseSlice []string) []string {
	mb := make(map[string]struct{}, len(baseSlice))
	for _, x := range baseSlice {
		mb[x] = struct{}{}
	}
	var diff []string
	for _, x := range newSlice {
		if _, found := mb[x]; !found {
			diff = append(diff, x)
		}
	}
	return diff
}

func DiffSlice(slice1 []string, slice2 []string) []string {
	var diff []string

	// Loop two times, first to find slice1 strings not in slice2,
	// second loop to find slice2 strings not in slice1
	for i := 0; i < 2; i++ {
		for _, s1 := range slice1 {
			found := false
			for _, s2 := range slice2 {
				if s1 == s2 {
					found = true
					break
				}
			}
			// String not found. We add it to return slice
			if !found {
				diff = append(diff, s1)
			}
		}
		// Swap the slices, only if it was the first loop
		if i == 0 {
			slice1, slice2 = slice2, slice1
		}
	}

	return diff
}

func ToUpperFirst(str string) string {
	if len(str) == 0 {
		return str
	}

	return fmt.Sprintf("%s%s", strings.ToUpper(str[0:1]), strings.ToLower(str[1:]))
}
func ToUpperEveryFirst(str string) string {
	if len(str) == 0 {
		return str
	}

	list := strings.Split(str, " ")

	var res []string
	for _, row := range list {
		res = append(res, fmt.Sprintf("%s%s", strings.ToUpper(row[0:1]), strings.ToLower(row[1:])))
	}

	return strings.Join(res, " ")
}

func GetIP(c *gin.Context) string {
	ip := c.Request.Header.Get("X-Forwarded-For")
	if ip == "" {
		ip = c.RemoteIP()
	}

	return ip
}

func Bool2Int(boolean bool) int {
	if boolean {
		return 1
	}
	return 0
}

func DayOfTheWeekToZhTw(dayOfTheWeek string) string {
	switch dayOfTheWeek {
	case "Monday":
		return "一"
	case "Tuesday":
		return "二"
	case "Wednesday":
		return "三"
	case "Thursday":
		return "四"
	case "Friday":
		return "五"
	case "Saturday":
		return "六"
	case "Sunday":
		return "日"
	default:
		return ""
	}
}

func ReplaceDayOfTheWeekFromEnToZhTw(timeString string) string {
	weekdayMap := map[string]string{
		"Mon": "一",
		"Tue": "二",
		"Wed": "三",
		"Thu": "四",
		"Fri": "五",
		"Sat": "六",
		"Sun": "日",
	}

	re := regexp.MustCompile(`\((Mon|Tue|Wed|Thu|Fri|Sat|Sun)\)`)

	replacedString := re.ReplaceAllStringFunc(timeString, func(match string) string {
		weekdayAbbreviation := match[1 : len(match)-1]
		return "(" + weekdayMap[weekdayAbbreviation] + ")"
	})

	return replacedString
}

func SliceIndexOf(need string, array []string) int {
	for k, v := range array {
		if need == v {
			return k
		}
	}
	return -1
}

func SliceReverse(s interface{}) interface{} {
	size := reflect.ValueOf(s).Len()
	swap := reflect.Swapper(s)
	for i, j := 0, size-1; i < j; i, j = i+1, j-1 {
		swap(i, j)
	}

	return s
}

// HtmlRemoveTags 清除 html 指定 tag (使用 html library)
func HtmlRemoveTags(n *html.Node, htmlTags []string) {
	for _, tag := range htmlTags {
		// if note is script tag
		if n.Type == html.ElementNode && n.Data == tag {
			n.Parent.RemoveChild(n)
			return
		}

		// traverse DOM
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			defer HtmlRemoveTags(c, []string{tag})
		}
	}
}

// HtmlRemoveTagsByRegular 清除 html 指定 tag (使用正規式)
func HtmlRemoveTagsByRegular(html string, htmlTags []string) string {
	for _, tag := range htmlTags {
		regex := regexp.MustCompile(fmt.Sprintf("<%s(.*?)<\\/%s>", tag, tag))

		html = regex.ReplaceAllString(html, "")
	}

	return html
}

// HtmlRemoveAJavascriptByRegular 清除 html 內含 javascript 的 a tag
func HtmlRemoveAJavascriptByRegular(html string) string {
	regex := regexp.MustCompile("(<a.*(javascript:).*\">.*<\\/a>)")
	html = regex.ReplaceAllString(html, "")

	return html
}

// HtmlCleanInjection 清除 html 注入攻擊
func HtmlCleanInjection(html string) string {
	html = HtmlRemoveTagsByRegular(html, []string{"javascript", "script"})
	return HtmlRemoveAJavascriptByRegular(html)
}

func StructToStringMap(obj interface{}) map[string]string {
	objValue := reflect.ValueOf(obj)
	if objValue.Kind() == reflect.Ptr {
		objValue = objValue.Elem()
	}

	if objValue.Kind() != reflect.Struct {
		return nil
	}

	objType := objValue.Type()
	result := make(map[string]string)

	for i := 0; i < objValue.NumField(); i++ {
		field := objType.Field(i)
		fieldValue := fmt.Sprintf("%v", objValue.Field(i))
		result[field.Name] = fieldValue
	}

	return result
}

// FormatDurationHumanFriendly formats duration from milliseconds to human-friendly format like 1h2m3s
func FormatDurationHumanFriendly(milliseconds int64) string {
	if milliseconds == 0 {
		return "0s"
	}

	duration := time.Duration(milliseconds) * time.Millisecond

	hours := int(duration.Hours())
	minutes := int(duration.Minutes()) % 60
	seconds := int(duration.Seconds()) % 60

	var parts []string

	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%dh", hours))
	}
	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%dm", minutes))
	}
	if seconds > 0 || len(parts) == 0 {
		parts = append(parts, fmt.Sprintf("%ds", seconds))
	}

	return strings.Join(parts, "")
}

// FormatDurationString formats duration string to limit decimal places to 3 digits
func FormatDurationString(d time.Duration) string {
	str := d.String()

	// Find decimal point in seconds part
	if strings.Contains(str, ".") && strings.HasSuffix(str, "s") {
		parts := strings.Split(str, ".")
		if len(parts) == 2 {
			secondsPart := parts[1]
			secondsPart = strings.TrimSuffix(secondsPart, "s")

			// Limit to 3 decimal places
			if len(secondsPart) > 3 {
				secondsPart = secondsPart[:3]
			}

			// Remove trailing zeros
			secondsPart = strings.TrimRight(secondsPart, "0")

			if secondsPart == "" {
				return parts[0] + "s"
			} else {
				return parts[0] + "." + secondsPart + "s"
			}
		}
	}

	return str
}
