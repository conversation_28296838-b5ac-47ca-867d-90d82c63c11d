package tool

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// OpenAIRequest represents the request structure for OpenAI Vision API
type OpenAIRequest struct {
	Model     string    `json:"model"`
	Messages  []Message `json:"messages"`
	MaxTokens int       `json:"max_tokens,omitempty"`
}

// Message represents a message in the OpenAI request
type Message struct {
	Role    string    `json:"role"`
	Content []Content `json:"content"`
}

// Content represents content in a message (text or image)
type Content struct {
	Type     string    `json:"type"`
	Text     string    `json:"text,omitempty"`
	ImageURL *ImageURL `json:"image_url,omitempty"`
}

// ImageURL represents an image URL in the content
type ImageURL struct {
	URL string `json:"url"`
}

// OpenAIResponse represents the response from OpenAI API
type OpenAIResponse struct {
	ID      string    `json:"id"`
	Object  string    `json:"object"`
	Created int64     `json:"created"`
	Model   string    `json:"model"`
	Choices []Choice  `json:"choices"`
	Usage   Usage     `json:"usage"`
	Error   *APIError `json:"error,omitempty"`
}

// Choice represents a choice in the OpenAI response
type Choice struct {
	Index   int             `json:"index"`
	Message ResponseMessage `json:"message"`
}

// ResponseMessage represents a message in the OpenAI response
type ResponseMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// Usage represents token usage information
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// APIError represents an error from the OpenAI API
type APIError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code"`
}

// UploadImageWithPrompt uploads an image file and prompt to OpenAI Vision API
// imageBytes: the image file content as bytes
// prompt: the text prompt to send along with the image
// apiKey: OpenAI API key
// Returns the response text from OpenAI
func UploadImageWithPrompt(imageBytes []byte, prompt string, apiKey string) (string, error) {
	if len(imageBytes) == 0 {
		return "", fmt.Errorf("image bytes cannot be empty")
	}
	if prompt == "" {
		return "", fmt.Errorf("prompt cannot be empty")
	}
	if apiKey == "" {
		return "", fmt.Errorf("API key cannot be empty")
	}

	// Encode image to base64
	base64Image := base64.StdEncoding.EncodeToString(imageBytes)
	imageDataURL := fmt.Sprintf("data:image/jpeg;base64,%s", base64Image)

	// Create the request payload
	request := OpenAIRequest{
		Model: "gpt-4.1-2025-04-14",
		Messages: []Message{
			{
				Role: "user",
				Content: []Content{
					{
						Type: "text",
						Text: prompt,
					},
					{
						Type: "image_url",
						ImageURL: &ImageURL{
							URL: imageDataURL,
						},
					},
				},
			},
		},
		MaxTokens: 300,
	}

	// Convert request to JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %v", err)
	}

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 60 * time.Second,
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", "https://api.openai.com/v1/chat/completions", bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request to OpenAI: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %v", err)
	}

	// Parse response
	var openAIResponse OpenAIResponse
	err = json.Unmarshal(responseBody, &openAIResponse)
	if err != nil {
		return "", fmt.Errorf("failed to parse response: %v", err)
	}

	// Check for API errors
	if openAIResponse.Error != nil {
		return "", fmt.Errorf("OpenAI API error: %s (type: %s, code: %s)",
			openAIResponse.Error.Message,
			openAIResponse.Error.Type,
			openAIResponse.Error.Code)
	}

	// Check if we have choices
	if len(openAIResponse.Choices) == 0 {
		return "", fmt.Errorf("no choices returned from OpenAI API")
	}

	// Check HTTP status code
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("OpenAI API returned status code %d: %s", resp.StatusCode, string(responseBody))
	}

	// Extract the response text
	responseText := ""
	if len(openAIResponse.Choices) > 0 {
		responseText = openAIResponse.Choices[0].Message.Content
	}

	if responseText == "" {
		return "", fmt.Errorf("no text content found in OpenAI response")
	}

	return responseText, nil
}

// UploadImageWithPromptAdvanced provides more control over the OpenAI Vision API request
// imageBytes: the image file content as bytes
// prompt: the text prompt to send along with the image
// apiKey: OpenAI API key
// model: OpenAI model to use (e.g., "gpt-4-vision-preview")
// maxTokens: maximum number of tokens in the response
// Returns the full OpenAI response for advanced usage
func UploadImageWithPromptAdvanced(imageBytes []byte, prompt string, apiKey string, model string, maxTokens int) (*OpenAIResponse, error) {
	if len(imageBytes) == 0 {
		return nil, fmt.Errorf("image bytes cannot be empty")
	}
	if prompt == "" {
		return nil, fmt.Errorf("prompt cannot be empty")
	}
	if apiKey == "" {
		return nil, fmt.Errorf("API key cannot be empty")
	}
	if model == "" {
		model = "gpt-4-vision-preview"
	}
	if maxTokens <= 0 {
		maxTokens = 300
	}

	// Encode image to base64
	base64Image := base64.StdEncoding.EncodeToString(imageBytes)
	imageDataURL := fmt.Sprintf("data:image/jpeg;base64,%s", base64Image)

	// Create the request payload
	request := OpenAIRequest{
		Model: model,
		Messages: []Message{
			{
				Role: "user",
				Content: []Content{
					{
						Type: "text",
						Text: prompt,
					},
					{
						Type: "image_url",
						ImageURL: &ImageURL{
							URL: imageDataURL,
						},
					},
				},
			},
		},
		MaxTokens: maxTokens,
	}

	// Convert request to JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 60 * time.Second,
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", "https://api.openai.com/v1/chat/completions", bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request to OpenAI: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// Parse response
	var openAIResponse OpenAIResponse
	err = json.Unmarshal(responseBody, &openAIResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	// Check for API errors
	if openAIResponse.Error != nil {
		return nil, fmt.Errorf("OpenAI API error: %s (type: %s, code: %s)",
			openAIResponse.Error.Message,
			openAIResponse.Error.Type,
			openAIResponse.Error.Code)
	}

	// Check HTTP status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("OpenAI API returned status code %d: %s", resp.StatusCode, string(responseBody))
	}

	return &openAIResponse, nil
}

// CleanOpenAIResponse removes markdown code block delimiters from OpenAI response
// Handles both formats:
// - Input with delimiters: "```json\n{...}\n```" → Output: "{...}"
// - Input without delimiters: "{...}" → Output: "{...}" (unchanged)
func CleanOpenAIResponse(response string) string {
	// Trim whitespace
	response = strings.TrimSpace(response)

	// Check if response starts with ```json and ends with ```
	if strings.HasPrefix(response, "```json") && strings.HasSuffix(response, "```") {
		// Remove the opening ```json and closing ```
		response = strings.TrimPrefix(response, "```json")
		response = strings.TrimSuffix(response, "```")
		// Trim any remaining whitespace/newlines
		response = strings.TrimSpace(response)
	} else if strings.HasPrefix(response, "```") && strings.HasSuffix(response, "```") {
		// Handle generic ``` code blocks (without json specifier)
		response = strings.TrimPrefix(response, "```")
		response = strings.TrimSuffix(response, "```")
		// Trim any remaining whitespace/newlines
		response = strings.TrimSpace(response)
	}

	return response
}
