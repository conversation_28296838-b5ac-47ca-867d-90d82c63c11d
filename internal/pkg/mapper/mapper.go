package mapper

import (
	"github.com/mitchellh/mapstructure"
)

// DecodeWithHooks 範例：
//
//	mapper.DecodeWithHooks(from, &to, hook1(), hook2(),...))
func DecodeWithHooks(input interface{}, output interface{}, hook ...mapstructure.DecodeHookFunc) error {
	config := &mapstructure.DecoderConfig{
		DecodeHook: mapstructure.ComposeDecodeHookFunc(hook...),
		Result:     &output,
	}

	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return err
	}

	if input != nil {
		decoder.Decode(input)
	}

	return nil
}
