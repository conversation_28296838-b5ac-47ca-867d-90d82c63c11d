package dto

type ShipSuite struct {
	ShipUrlName     string           // 郵輪名稱
	SuiteName       string           // 套房名稱
	Overview        string           // 套房概覽
	Amenities       string           // 套房佈局與設施 (Layout + Amenities combined)
	VirtualTourUrl  string           // 虛擬導覽網址
	ImageS3Paths    [][]string       // 套房圖片 S3 路徑 (一間套房可能有1~多種 category，每種底下都有1~多張照片)
	CategoryDetails []CategoryDetail // 套房類別詳細資訊，可留空
}

type CategoryDetail struct {
	Category    string // 套房類別
	SuiteSize   string // 套房大小
	BalconySize string // 陽台大小
	Decks       string // 所在甲板層
}
