package common

type GeneralFailedResponse struct {
	Success bool          `json:"success" example:"false"`           // 成功失敗
	Code    string        `json:"code,omitempty" example:"vd0001"`   // 錯誤代碼(au=Authorized, vd=Validation, nf=NotFound, db=Database, rd=Redis,bu=BusinessLogic,pe=Permission,ps=Parse,ca=CallApi,uk=Unknown
	Message string        `json:"message,omitempty" example:"false"` // 錯誤訊息
	Data    validationRes `json:"data"`                              // 錯誤細節
} // @name GeneralFailedResponse
