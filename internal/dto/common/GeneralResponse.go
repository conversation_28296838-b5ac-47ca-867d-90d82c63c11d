package common

import (
	"errors"
	"fmt"
	"io"
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/claim"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/language"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/log_topic"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/status"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/loki"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"github.com/gin-gonic/gin"
)

type GeneralResponse struct {
	Success bool        `json:"success"`           // 成功失敗
	Code    string      `json:"code,omitempty"`    // 錯誤代碼(au=Authorized, vd=Validation, nf=NotFound, db=Database, rd=Redis,bu=BusinessLogic,pe=Permission,ps=Parse,ca=CallApi,ep=Export,uk=Unknown)
	Message string      `json:"message,omitempty"` // 訊息
	Paging  *Paging     `json:"paging,omitempty"`  // 分頁
	Data    interface{} `json:"data"`              // 資料
	Error   error       `json:"error,omitempty" swaggerignore:"true"`
	NeedLog bool        `json:"needLog,omitempty" swaggerignore:"true"` // 強制 Log
}

func (resp GeneralResponse) Log() GeneralResponse {
	resp.NeedLog = true
	return resp
}

func (resp GeneralResponse) Status(c *gin.Context) GeneralResponse {
	return resp.StatusWithHook(c, nil, nil)
}

// StatusWithHook 狀態處理帶事件處理
func (resp GeneralResponse) StatusWithHook(c *gin.Context, req interface{}, hook func(c *gin.Context, req interface{}, res GeneralResponse)) GeneralResponse {

	// 自動校正錯誤碼
	if tool.InArray(resp.Code, []string{status.DatabaseSelect}) && resp.Error != nil {
		if errors.Is(resp.Error, gorm.ErrRecordNotFound) {
			resp.Code = status.NotFound
		}
	}

	// effect response
	var langMsg = CodeToMessage(c.GetString(claim.Lang), resp.Code)
	tmpResp := resp
	if tool.InArray(resp.Code, status.Internal) {
		tmpResp.Message = langMsg
	} else if langMsg == "" {
		tmpResp.Message = resp.Message
	} else {
		tmpResp.Message = strings.Join([]string{langMsg, resp.Message}, ": ")
	}
	if resp.Message == "" {
		tmpResp.Message = langMsg
	}

	stringError := ""
	if resp.Error != nil {
		stringError = resp.Error.Error()
	}
	// Log first
	if tool.InArray(resp.Code, status.NeedErrorLog) {
		dtoLog := loki.LokiLog{Level: loki.ERROR, Topic: log_topic.Response, Subtitle: resp.Code, Message: tmpResp.Message, Data: stringError}
		loki.Log(dtoLog)
		// Log to ES
		fmt.Println("LokiLog", tool.ToJson(dtoLog))
	} else if tool.InArray(resp.Code, status.NeedWarnLog) {
		loki.Log(loki.LokiLog{Level: loki.WARNING, Topic: log_topic.Response, Subtitle: resp.Code, Message: tmpResp.Message, Data: stringError})
	} else if resp.NeedLog {
		dtoLog := loki.LokiLog{Level: loki.ERROR, Topic: log_topic.Response, Subtitle: resp.Code, Message: tmpResp.Message, Data: stringError}
		loki.Log(dtoLog)
		// Log to ES
		fmt.Println("LokiLog", tool.ToJson(dtoLog))
	} else if resp.Code == "" && resp.Message == "" && stringError != "" {
		tmpResp.Message = stringError
	}

	if resp.Code == status.Validation {
		if resp.Error != nil {
			if errors.Is(resp.Error, io.ErrUnexpectedEOF) {
				loki.Log(loki.LokiLog{Level: loki.WARNING, Topic: log_topic.Response, Subtitle: resp.Code, Message: "If you have multiple BindJson, change to ShouldBindBodyWith(&, binding.JSON), or check frontend request data is empty", Data: resp.Error.Error()})
				tmpResp.Code = status.ValidationBodyRequired
				tmpResp.Message = CodeToMessage(c.GetString(claim.Lang), tmpResp.Code)
			}
			msgArray, validationResponses := formatValidation(c.GetString(claim.Lang), resp.Error)
			if len(validationResponses) > 0 {
				tmpResp.Message = strings.Join(msgArray, "\n")
				tmpResp.Data = validationResponses
				tmpResp.Code = validationResponses[0].Code
			} else {
				loki.Log(loki.LokiLog{Level: loki.WARNING, Topic: log_topic.Response, Subtitle: resp.Code, Message: "undefined error", Data: resp.Error.Error()})
			}
		} else {
			loki.Log(loki.LokiLog{Level: loki.WARNING, Topic: log_topic.Response, Subtitle: resp.Code, Message: "undefined error"})
		}
	}

	tmpResp.Error = nil

	if hook != nil {
		hook(c, req, tmpResp)
	}

	return tmpResp
}

type validationRes struct {
	Tag   string `json:"tag" example:"len"`            // 驗證名稱
	Field string `json:"field" example:"orderId"`      // 欄位
	Param string `json:"param,omitempty" example:"10"` // 參數
	Code  string `json:"code" swaggerignore:"true"`    // 對應狀態碼
}

func formatValidation(lang string, err error) ([]string, []validationRes) {
	if validationErrs, ok := err.(validator.ValidationErrors); ok {
		errorMessages := make([]string, len(validationErrs))
		var validationResponses []validationRes
		for i, e := range validationErrs {
			tmpRes := validationRes{
				Tag:   e.Tag(),
				Field: e.Field(),
				Param: e.Param(),
			}
			switch e.Tag() {
			// Fields
			case "eqfield":
				tmpRes.Code = status.ValidationEqField
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field(), e.Param())
				break

			// Network
			case "ip":
				tmpRes.Code = status.ValidationIp
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break

			// String
			case "alpha":
				tmpRes.Code = status.ValidationAlpha
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "alphanum":
				tmpRes.Code = status.ValidationAlphaNum
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "contains":
				tmpRes.Code = status.ValidationContains
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field(), e.Param())
				break
			case "lowercase":
				tmpRes.Code = status.ValidationLowercase
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "uppercase":
				tmpRes.Code = status.ValidationUppercase
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "numeric":
				tmpRes.Code = status.ValidationNumeric
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break

			// Format
			case "email":
				tmpRes.Code = status.ValidationEmail
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "datetime":
				tmpRes.Code = status.ValidationDatetime
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field(), e.Param())
				break

			// Comparisons
			case "eq":
				tmpRes.Code = status.ValidationEq
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field(), e.Param())
				break
			case "ne":
				tmpRes.Code = status.ValidationNe
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field(), e.Param())
				break
			case "gt":
				tmpRes.Code = status.ValidationGt
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "gte":
				tmpRes.Code = status.ValidationGte
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "lt":
				tmpRes.Code = status.ValidationLt
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "lte":
				tmpRes.Code = status.ValidationLte
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "len":
				tmpRes.Code = status.ValidationLen
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field(), e.Param())
				break
			case "max":
				tmpRes.Code = status.ValidationMax
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field(), e.Param())
				break
			case "min":
				tmpRes.Code = status.ValidationMin
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field(), e.Param())
				break
			case "required":
				tmpRes.Code = status.ValidationRequired
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "oneof":
				tmpRes.Code = status.ValidationOneOf
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field(), e.Param())
				break

			// Customize
			case "order_id":
				tmpRes.Code = status.ValidationOrderId
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "ymd":
				tmpRes.Code = status.ValidationYmd
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			case "contains_if":
				tmpRes.Code = status.ValidationContains
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field(), e.Param())
				break
			case "password":
				tmpRes.Code = status.ValidationPassword
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())
				break
			default:
				tmpRes.Code = status.ValidationUndefined
				errorMessages[i] = fmt.Sprintf(CodeToMessage(lang, tmpRes.Code), e.Field())

			}
			validationResponses = append(validationResponses, tmpRes)
		}
		return errorMessages, validationResponses
	} else if tool.CaseInsensitiveContains(err.Error(), "json: cannot unmarshal") {
		var patten = regexp.MustCompile(`(?mi)json: cannot unmarshal .* Go struct field (.*) of type (.*)`)
		matches := patten.FindStringSubmatch(err.Error())
		if len(matches) == 3 {
			fields := strings.Split(matches[1], ".")
			shortFieldName := fields[len(fields)-1]
			validationResponses := []validationRes{
				validationRes{
					Tag:   "type",
					Field: shortFieldName,
					Param: matches[2],
					Code:  status.ValidationTypeInvalid,
				},
			}
			errorMessages := []string{
				fmt.Sprintf(CodeToMessage(lang, status.ValidationFormat), shortFieldName, matches[2]),
			}
			return errorMessages, validationResponses
		}
	}
	return []string{err.Error()}, nil
}

func CodeToMessage(lang string, code string) string {
	if lang != "" {
		if lang == language.ZhTw {
			return status.MessageZhTW[code]
		} else if lang == language.ZhCn {
			return status.MessageZhCN[code]
		}
	}
	return status.MessageEn[code]
}
