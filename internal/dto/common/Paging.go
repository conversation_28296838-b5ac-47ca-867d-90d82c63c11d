package common

type Paging struct {
	NumberPerPage int   `json:"numberPerPage,omitempty" example:"10" binding:"gte=1"`         // 每頁需求筆數
	PageNumber    int64 `json:"pageNumber,omitempty" example:"1" binding:"gte=1"`             // 目前所在頁數 (從 1 開始)
	TotalPageSize int64 `json:"totalPageSize,omitempty" readonly:"true" swaggerignore:"true"` // 總頁數
	TotalSize     int64 `json:"totalSize,omitempty" readonly:"true"`                          // 總筆數
	IsLastPage    bool  `json:"isLastPage,omitempty" readonly:"true"`                         // 是否為最後一頁
} // @name Paging

func (r *Paging) GetOffset() int {

	page := int(r.PageNumber)

	pageSize := r.NumberPerPage
	switch {
	case pageSize <= 0:
		pageSize = 0
	}

	offset := (page - 1) * pageSize
	return offset
}
