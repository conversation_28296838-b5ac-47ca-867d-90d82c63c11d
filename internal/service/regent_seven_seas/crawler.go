package regent_seven_seas

import (
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
	"github.com/go-rod/rod"
)

type RegentSevenSeasCrawler struct {
	shipUrlName string
	isHeadless  bool
	timeout     time.Duration
}

func NewCrawler(shipUrlName string, isHeadless bool, timeout time.Duration) *RegentSevenSeasCrawler {
	return &RegentSevenSeasCrawler{
		shipUrlName: shipUrlName,
		isHeadless:  isHeadless,
		timeout:     timeout,
	}
}

func (crawler *RegentSevenSeasCrawler) setupPage(url string) (page *rod.Page, teardown func(), err error) {
	browser := service.LaunchBrowser(crawler.isHeadless)
	teardown = func() {
		browser.MustClose()
	}

	page = browser.MustPage()
	err = service.NavigateWithStatusCheck(page, url, crawler.timeout)
	if err != nil {
		return nil, teardown, err
	}

	return page, teardown, nil
}
