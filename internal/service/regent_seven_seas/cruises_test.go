package regent_seven_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipCruises(t *testing.T) {
	shipCruisesUrlMap := map[string]string{
		"grandeur": "https://www.rssc.com/ships/seven_seas_grandeur",
		// "splendor":  "https://www.rssc.com/ships/seven_seas_splendor",
		// "explorer":  "https://www.rssc.com/ships/seven_seas_explorer",
		// "voyager":   "https://www.rssc.com/ships/seven_seas_voyager",
		// "mariner":   "https://www.rssc.com/ships/seven_seas_mariner",
		// "navigator": "https://www.rssc.com/ships/seven_seas_navigator",
	}

	for shipUrlName, shipCruisesUrl := range shipCruisesUrlMap {
		crawler := NewCrawler(shipUrlName, true, 30*time.Second)

		cruises, err := crawler.CrawlShipCruises(shipCruisesUrl)
		if err != nil {
			t.Fatalf("Failed to crawl Regent Seven Seas: %v", err)
		}

		for _, cruise := range cruises {
			fmt.Printf("Cruise: %+v\n", cruise)
		}

		fmt.Printf("Total cruises: %d\n", len(cruises))
	}
}
