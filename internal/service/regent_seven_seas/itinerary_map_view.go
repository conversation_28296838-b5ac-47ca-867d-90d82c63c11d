package regent_seven_seas

import (
	"fmt"
	"strings"
	"time"

	"github.com/go-rod/rod/lib/proto"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

// CrawlShipItineraryMapView crawls and captures the map view of an itinerary
func (crawler *RegentSevenSeasCrawler) CrawlShipItineraryMapView(url string) (string, error) {
	page, teardown, err := crawler.setupPage(url)
	if err != nil {
		return "", err
	}
	defer teardown()

	service.ScrollPageDown(page, 10, 100, 1)

	var mapViewS3Path string
	mapViewTab, err := page.Element("a[title='Map View']")
	if err != nil {
		service.Logger.Error().Err(err).Msg("Map View tab not found")
		return "", nil
	}

	if mapViewTab == nil {
		service.Logger.Info().Msg("Map View tab is nil")
		return "", nil
	}

	parentLi, err := mapViewTab.Parent()
	if err != nil {
		service.Logger.Error().Err(err).Msg("Failed to get parent element of Map View tab")
		return "", nil
	}

	style, err := parentLi.Attribute("style")
	isHidden := false
	if err == nil && style != nil {
		isHidden = strings.Contains(*style, "display: none")
	}

	if isHidden {
		service.Logger.Info().Msg("Map View tab is hidden")
		return "", nil
	}

	mapViewTab.MustClick()
	time.Sleep(2 * time.Second)

	mapCanvas, err := page.Element("#m58_c56_body")
	if err != nil {
		service.Logger.Error().Err(err).Msg("Error: Canvas element not found")
		return "", fmt.Errorf("canvas element not found: %w", err)
	}
	service.Logger.Info().Msg("Successfully located the element")

	screenshotBytes, err := mapCanvas.Screenshot(proto.PageCaptureScreenshotFormatPng, 90)
	if err != nil {
		service.Logger.Error().Err(err).Msg("Error: Failed to capture canvas screenshot")
		return "", fmt.Errorf("failed to capture screenshot: %w", err)
	}

	urlIdentifier := strings.Split(url, "/")[len(strings.Split(url, "/"))-1]
	s3Path := fmt.Sprintf("ship_%s/regent-seven-seas/%s", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)
	s3FileName := fmt.Sprintf("itinerary_%s_%s.png", crawler.shipUrlName, urlIdentifier)

	err = service.S3AddFile(screenshotBytes, s3FileName, s3Path)
	if err != nil {
		service.Logger.Error().Err(err).Msg("Error: Failed to upload to S3")
		return "", fmt.Errorf("failed to upload to S3: %w", err)
	}

	mapViewS3Path = fmt.Sprintf("%s/%s", s3Path, s3FileName)
	service.Logger.Info().Msgf("Successfully uploaded screenshot to S3: %s", mapViewS3Path)

	return mapViewS3Path, nil
}
