package regent_seven_seas

import (
	"fmt"
	"path/filepath"
	"strings"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

func (crawler *RegentSevenSeasCrawler) CrawlShipSuites(shipSuitesUrl string) (suites []dto.ShipSuite, err error) {
	page, teardown, err := crawler.setupPage(shipSuitesUrl)
	if err != nil {
		return nil, err
	}
	defer teardown()

	service.ScrollPageDown(page, 5, 1500, 1)

	suiteElements, err := page.Elements("div.c29_body li.listing_item")
	if err != nil {
		return nil, fmt.Errorf("failed to find suite listing items: %w", err)
	}

	suiteS3Path := fmt.Sprintf("ship_%s/regent-seven-seas/%s/suites", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)
	for _, suiteElement := range suiteElements {
		suite := dto.ShipSuite{
			ShipUrlName: crawler.shipUrlName,
		}
		titleElement, err := suiteElement.Element("div.c27 div.c27_header_title h3.headline")
		if err == nil && titleElement != nil {
			suite.SuiteName = strings.TrimSpace(titleElement.MustText())
		}

		// find virtual tour url
		virtualTourElement, err := suiteElement.Element("a.e5.btn.btn-link.btn-trim[title='Virtual Tour']")
		virtualTourUrl := ""
		if err == nil && virtualTourElement != nil {
			hrefAttr, err := virtualTourElement.Attribute("href")
			if err == nil && hrefAttr != nil {
				virtualTourUrl = *hrefAttr
			}
		}
		suite.VirtualTourUrl = virtualTourUrl

		suites = append(suites, suite)
	}

	c32Elements, err := page.Elements("div.c32")
	if err == nil && len(c32Elements) > 0 {
		for i, modal := range c32Elements {
			if i < len(suites) {
				overview, err := modal.Element("div.c43_body div.paragraph.-small")
				if err == nil && overview != nil {
					overviewText := strings.TrimSpace(overview.MustText())
					suites[i].Overview = overviewText
				}

				var amenities string
				layoutItems, err := modal.Elements(".c44_item[data-js='c44-item-1'] .paragraph.-small ul li")
				if err == nil && len(layoutItems) > 0 {
					for _, item := range layoutItems {
						layout := strings.TrimSpace(item.MustText())
						if layout != "" {
							amenities += "\n " + layout
						}
					}
				}

				amenityItems, err := modal.Elements(".c44_item[data-js='c44-item-2'] .paragraph.-small ul li")
				if err == nil && len(amenityItems) > 0 {
					for _, item := range amenityItems {
						amenity := strings.TrimSpace(item.MustText())
						if amenity != "" {
							amenities += "\n " + amenity
						}
					}
				}

				suites[i].Amenities = strings.TrimSpace(amenities)

				var categoryDetails []dto.CategoryDetail
				categoryElements, err := modal.Elements(".c38_list_item span.c38_link_text")
				if err == nil && len(categoryElements) > 0 {
					for _, catElement := range categoryElements {
						categoryText := strings.TrimSpace(catElement.MustText())
						if categoryText != "" {
							categoryDetails = append(categoryDetails, dto.CategoryDetail{Category: categoryText})
						}
					}
				}
				if len(categoryDetails) == 0 {
					categoryDetails = append(categoryDetails, dto.CategoryDetail{Category: ""})
				}
				suites[i].CategoryDetails = categoryDetails

				c45Elements, err := modal.Elements("div.c45")
				if err == nil && len(c45Elements) > 0 {
					for idx, c45 := range c45Elements {
						listingItems, err := c45.Elements("ul.listing li.listing_item")
						if err == nil && len(listingItems) > 0 {
							targetIdx := idx
							if targetIdx >= len(suites[i].CategoryDetails) {
								targetIdx = len(suites[i].CategoryDetails) - 1
							}
							for _, item := range listingItems {
								labelElement, err := item.Element(".c35_label")
								if err == nil && labelElement != nil {
									label := strings.TrimSpace(labelElement.MustText())

									valueElement, err := item.Element(".c35_size .numbers")
									if err == nil && valueElement != nil {
										value := strings.TrimSpace(valueElement.MustText())
										switch {
										case strings.Contains(strings.ToLower(label), "suite"):
											if suites[i].CategoryDetails[targetIdx].SuiteSize != "" {
												suites[i].CategoryDetails[targetIdx].SuiteSize += ", " + value
											} else {
												suites[i].CategoryDetails[targetIdx].SuiteSize = value
											}
										case strings.Contains(strings.ToLower(label), "balcony"):
											if suites[i].CategoryDetails[targetIdx].BalconySize != "" {
												suites[i].CategoryDetails[targetIdx].BalconySize += ", " + value
											} else {
												suites[i].CategoryDetails[targetIdx].BalconySize = value
											}
										case strings.Contains(strings.ToLower(label), "deck"):
											if suites[i].CategoryDetails[targetIdx].Decks != "" {
												suites[i].CategoryDetails[targetIdx].Decks += ", " + value
											} else {
												suites[i].CategoryDetails[targetIdx].Decks = value
											}
										}
									}
								}
							}
						}
					}
				}

				// Extract the desktop version image from modal
				c31Elements, err := modal.Elements("div.c31")
				if err != nil {
					service.Logger.Error().
						Err(err).
						Int("modalIndex", i).
						Str("shipUrlName", crawler.shipUrlName).
						Msg("Error finding c31 elements in modal")
					continue
				}

				for j, c31Element := range c31Elements {
					c31TabElements, err := c31Element.Elements("div.c31_tab")
					if err != nil {
						service.Logger.Error().
							Err(err).
							Int("c31Index", j).
							Str("shipUrlName", crawler.shipUrlName).
							Msg("Error finding c31_tab elements")
						continue
					}

					var tabImagePaths [][]string

					for k, c31Tab := range c31TabElements {
						c31TabItems, err := c31Tab.Elements("div.c31_tab_item[data-js=\"c31-tab-item-0\"]")
						if err != nil {
							service.Logger.Error().
								Err(err).
								Int("c31TabIndex", k).
								Str("shipUrlName", crawler.shipUrlName).
								Msg("Error finding c31_tab_item elements")
							continue
						}

						var tabItemImagePaths []string

						for l, c31TabItem := range c31TabItems {
							c30ListItems, err := c31TabItem.Elements("ul.c30_list li.c30_list_item")
							if err != nil {
								service.Logger.Error().
									Err(err).
									Int("c31TabItemIndex", l).
									Str("shipUrlName", crawler.shipUrlName).
									Msg("Error finding c30_list_item elements")
								continue
							}

							for m, c30ListItem := range c30ListItems {
								imgElement, err := c30ListItem.Element("img.c5_figure_item.-desktop.visible-lg")
								if err != nil || imgElement == nil {
									service.Logger.Error().
										Err(err).
										Int("c30ListItemIndex", m).
										Str("shipUrlName", crawler.shipUrlName).
										Msg("Error finding desktop image")
									continue
								}

								srcAttr, err := imgElement.Attribute("src")
								if err != nil || srcAttr == nil {
									service.Logger.Error().
										Err(err).
										Int("c30ListItemIndex", m).
										Str("shipUrlName", crawler.shipUrlName).
										Msg("Error getting src attribute")
									continue
								}

								imageUrl := ""
								if strings.HasPrefix(*srcAttr, "/") {
									imageUrl = "https://www.rssc.com" + *srcAttr
								} else {
									imageUrl = *srcAttr
								}

								imageBytes, err := tool.FetchFileBytes(imageUrl)
								if err != nil {
									service.Logger.Error().Err(err).Msgf("Failed to fetch image: %s", imageUrl)
									continue
								}

								s3FileName := filepath.Base(*srcAttr)
								err = service.S3AddFile(imageBytes, s3FileName, suiteS3Path)
								if err != nil {
									service.Logger.Error().Err(err).Msgf("Failed to upload image to S3: %s", s3FileName)
								} else {
									fullS3Path := fmt.Sprintf("%s/%s", suiteS3Path, s3FileName)
									tabItemImagePaths = append(tabItemImagePaths, fullS3Path)
								}
							}
						}

						if len(tabItemImagePaths) > 0 {
							tabImagePaths = append(tabImagePaths, tabItemImagePaths)
						}
					}

					if len(tabImagePaths) > 0 {
						suites[i].ImageS3Paths = tabImagePaths
					}
				}
			}
		}
	}

	service.Logger.Info().
		Int("count", len(suites)).
		Str("url", shipSuitesUrl).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Suites crawled successfully")

	return suites, nil
}
