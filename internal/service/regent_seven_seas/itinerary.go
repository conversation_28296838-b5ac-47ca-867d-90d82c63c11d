package regent_seven_seas

import (
	"fmt"
	"strings"
	"time"

	"github.com/go-rod/rod"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

// CrawlShipItineraries crawls itinerary from cruise detail link
// be used to crawl itinerary from CrawlRegentSevenSeasCruises Res DetailLink
func (crawler *RegentSevenSeasCrawler) CrawlShipItineraries(shipItineraryUrl, from, to string) (itineraries []dto.ShipItinerary, isWaitlisted bool, price string, err error) {
	page, teardown, err := crawler.setupPage(shipItineraryUrl)
	if err != nil {
		return nil, false, "", err
	}
	defer teardown()

	service.ScrollPageDown(page, 5, 1500, 0)

	waitlistElems, err := page.Elements(".listing_item.-cta .e27_item.-primaryInfo .cardText.-variant-7")
	if err == nil {
		for _, elem := range waitlistElems {
			text := strings.TrimSpace(elem.MustText())
			if strings.Contains(text, "WAITLISTED") {
				isWaitlisted = true
				service.Logger.Info().Msgf("Found waitlisted status for %s", shipItineraryUrl)
				break
			}
		}
	}

	priceElems, err := page.Elements("div.c203 .listing_item")
	if err != nil {
		service.Logger.Error().Err(err).Msgf("Failed to find price elements for %s", shipItineraryUrl)
		return nil, isWaitlisted, "", nil
	}

	for _, elem := range priceElems {
		fareAttr, err := elem.Attribute("data-fare")
		if err == nil && fareAttr != nil && *fareAttr != "" {
			fareValue := strings.TrimSpace(*fareAttr)
			if fareValue != "0" && fareValue != "" {
				price = fmt.Sprintf("$%s", fareValue)
				service.Logger.Info().Msgf("Found price %s for %s", price, shipItineraryUrl)
				break
			}
		}
	}

	listViewTab, err := page.Element("a[title='List View']")
	if err == nil && listViewTab != nil {
		listViewTab.MustClick()
		time.Sleep(500 * time.Millisecond)
	}

	rows, err := page.Elements("tr.c179_table_body_row")
	if err != nil {
		return nil, false, "", fmt.Errorf("failed to find itinerary rows: %w", err)
	}

	for i, row := range rows {
		itinerary := dto.ShipItinerary{}

		dateCell, err := row.Element("div.e27.-variant-6")
		if err == nil && dateCell != nil {
			primaryInfo, err := dateCell.Element("div.e27_item.-primaryInfo")
			secondaryInfo, err2 := dateCell.Element("div.e27_item.-secondaryInfo")

			if err == nil && err2 == nil && primaryInfo != nil && secondaryInfo != nil {
				month := primaryInfo.MustText()
				day := secondaryInfo.MustText()
				itinerary.Date = day + " " + month
			}
		}

		portCell, err := row.Element("td.c179_details_row_cell.-port")
		if err == nil && portCell != nil {
			portInfo, err := portCell.Element("div.c179_details_data_item.-port-city")
			countryInfo, err2 := portCell.Element("div.c179_details_data_item.-port-country")

			if err == nil && portInfo != nil {
				port := strings.TrimSpace(portInfo.MustText())
				// Remove trailing comma if present
				port = strings.TrimSuffix(port, ",")
				itinerary.Port = port
			}

			if err2 == nil && countryInfo != nil {
				itinerary.Country = strings.TrimSpace(countryInfo.MustText())
			}
		}

		arriveCell, err := row.Element("td.c179_details_row_cell.-arrive")
		itinerary.ArriveTime = ""
		if err == nil && arriveCell != nil {
			arriveInfo, err := arriveCell.Element("div.e27_item.-primaryInfo")
			if err == nil && arriveInfo != nil {
				itinerary.ArriveTime = strings.TrimSpace(arriveInfo.MustText())
			}
		}

		departCell, err := row.Element("td.c179_details_row_cell.-depart")
		itinerary.DepartTime = ""
		if err == nil && departCell != nil {
			departInfo, err := departCell.Element("div.e27_item.-primaryInfo")
			if err == nil && departInfo != nil {
				itinerary.DepartTime = strings.TrimSpace(departInfo.MustText())
			}
		}

		if i < len(rows) {
			clickableArea, err := row.Element("td.c179_table_body_row_cell.-content")
			if err == nil && clickableArea != nil {
				clickableArea.MustScrollIntoView()

				time.Sleep(300 * time.Millisecond)

				clickableArea.MustClick()
				time.Sleep(1 * time.Second)

				// modal 可能出現較慢，等待並重試檢查是否 visible
				isVisible := false
				maxRetries := 3
				var modal *rod.Element
				var err error

				// modal 可能有點擊後沒彈出的情況，所以需要檢查是否 visible (not display:none)
				// 如果 isVisible 為 false，重試 3 次確認，每次重新抓取 modal 元素
				for retry := 0; retry < maxRetries; retry++ {
					// 等待一段時間讓 modal 有機會出現
					time.Sleep(2 * time.Second)

					modal, err = page.Element("#modal-m58-itinerary-overlay")
					if err != nil {
						break
					}
					if modal == nil {
						continue
					}

					isVisible, err = modal.Visible()
					if err != nil {
						break
					}
					if isVisible {
						break
					}
				}

				if err != nil || modal == nil || !isVisible {
					continue
				}

				descriptionFound := make(chan bool, 1)
				go func() {
					descriptionBlock, err := modal.Element(".c185_body_block .paragraph.-small p")
					if err == nil && descriptionBlock != nil {
						var destinationInfo string
						destinationInfo, err = descriptionBlock.Text()
						if err == nil {
							destinationInfo = strings.TrimSpace(destinationInfo)

							itinerary.DestinationInfo = destinationInfo

							descriptionFound <- true
							return
						}
					}
					descriptionFound <- false
				}()

				select {
				case <-descriptionFound:
				case <-time.After(1 * time.Second):
				}

				// Find and fetch tablet image
				imageElement, err := modal.Element("img.c5_figure_item.-tablet.visible-md")
				if err == nil && imageElement != nil {
					srcAttr, err := imageElement.Attribute("src")
					if err == nil && srcAttr != nil {
						imageUrl := ""
						if strings.HasPrefix(*srcAttr, "/") {
							imageUrl = "https://www.rssc.com" + *srcAttr
						} else {
							imageUrl = *srcAttr
						}

						imageBytes, err := tool.FetchFileBytes(imageUrl)
						if err != nil {
							service.Logger.Error().
								Err(err).
								Str("imageUrl", imageUrl).
								Str("port", itinerary.Port).
								Msg("Failed to fetch itinerary image")
						} else {
							s3Path := fmt.Sprintf("ship_%s/regent-seven-seas/%s/itineraries", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)

							// Create filename using from_to and day
							fromName := strings.ToLower(strings.ReplaceAll(from, " ", "_"))
							toName := strings.ToLower(strings.ReplaceAll(to, " ", "_"))
							dateParts := strings.Split(itinerary.Date, " ")
							month := dateParts[1]
							day := dateParts[2]
							s3FileName := fmt.Sprintf("%s_%s_%s_%s.jpg", fromName, toName, month, day)

							err = service.S3AddFile(imageBytes, s3FileName, s3Path)
							if err != nil {
								service.Logger.Error().
									Err(err).
									Str("s3FileName", s3FileName).
									Str("port", itinerary.Port).
									Msg("Failed to upload itinerary image to S3")
							} else {
								fullS3Path := fmt.Sprintf("%s/%s", s3Path, s3FileName)
								itinerary.ImageS3Path = fullS3Path

								service.Logger.Info().
									Str("s3Path", fullS3Path).
									Str("port", itinerary.Port).
									Msg("Successfully uploaded itinerary image to S3")
							}
						}
					}
				}

				page.MustElement(".c185_header_item a.e32[title='Close']").MustClick()
			}
		}
		itineraries = append(itineraries, itinerary)
	}

	return itineraries, isWaitlisted, price, nil
}
