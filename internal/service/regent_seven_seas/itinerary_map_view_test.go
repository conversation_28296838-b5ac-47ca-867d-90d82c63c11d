package regent_seven_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipItineraryMapView(t *testing.T) {
	testCases := map[string][]string{
		"grandeur": {
			"https://www.rssc.com/cruises/GRA260125/summary",
			"https://www.rssc.com/cruises/GRA250727/summary",
			"https://www.rssc.com/cruises/GRA251025/summary",
		},
	}

	for shipUrlName, urls := range testCases {
		for _, url := range urls {
			crawler := NewCrawler(shipUrlName, true, 30*time.Second)

			mapViewS3Path, err := crawler.CrawlShipItineraryMapView(url)
			if err != nil {
				t.Fatalf("Failed to crawl itinerary map view for %s: %v", url, err)
			}

			if mapViewS3Path != "" {
				fmt.Printf("Successfully captured map view. S3 Path: %s\n", mapViewS3Path)
			} else {
				fmt.Printf("No map view available for this itinerary\n")
			}
		}
	}
}
