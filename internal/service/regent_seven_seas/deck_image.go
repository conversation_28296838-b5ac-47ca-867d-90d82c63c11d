package regent_seven_seas

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/proto"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

// CrawlShipDeckImages crawls and downloads deck images
func (crawler *RegentSevenSeasCrawler) CrawlShipDeckImages(shipDeckImageUrl string) (dto.ShipDeckImage, error) {
	page, teardown, err := crawler.setupPage(shipDeckImageUrl)
	if err != nil {
		return dto.ShipDeckImage{}, err
	}
	defer teardown()

	browser := page.Browser()
	router := browser.HijackRequests()
	defer router.Stop()

	var pdfUrl string
	router.MustAdd("*", func(ctx *rod.Hijack) {
		url := ctx.Request.URL().String()

		if strings.Contains(url, ".pdf") {
			pdfUrl = url
		}

		ctx.ContinueRequest(&proto.FetchContinueRequest{})
	})

	go router.Run()

	downloadBtn, err := page.Element("div.c130_aside a[title='Download']")
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("cannot find download button for URL %s: %w", shipDeckImageUrl, err)
	}

	href, err := downloadBtn.Attribute("href")
	if err == nil && href != nil && *href != "#" {
		service.Logger.Info().
			Str("downloadUrl", *href).
			Str("shipUrlName", crawler.shipUrlName).
			Msg("Found direct download URL from href")
	}

	err = downloadBtn.Click(proto.InputMouseButtonLeft, 1)
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to click download button: %w", err)
	}

	time.Sleep(2 * time.Second)

	if pdfUrl == "" {
		return dto.ShipDeckImage{}, fmt.Errorf("no download URLs intercepted for %s", shipDeckImageUrl)
	}

	service.Logger.Info().
		Str("pdfUrl", pdfUrl).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Found PDF URL, starting download")

	fileBytes, err := tool.FetchFileBytes(pdfUrl)
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to fetch PDF file: %w", err)
	}

	tempDir := filepath.Join("temp", "deck_images", crawler.shipUrlName)
	err = os.MkdirAll(tempDir, 0755)
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to create temp directory: %w", err)
	}

	pdfFileName := fmt.Sprintf("deck_image_%s.pdf", crawler.shipUrlName)
	pdfPath := filepath.Join(tempDir, pdfFileName)

	err = os.WriteFile(pdfPath, fileBytes, 0644)
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to save PDF to temp file: %w", err)
	}

	service.Logger.Info().
		Str("pdfPath", pdfPath).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("PDF saved to project temp file")

	imageOutputDir := filepath.Join(tempDir, "images")
	imageFiles, err := tool.ConvertPDFToImages(pdfPath, imageOutputDir)
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to convert PDF to images: %w", err)
	}

	service.Logger.Info().
		Int("imageCount", len(imageFiles)).
		Str("outputDir", imageOutputDir).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Successfully converted PDF to images")

	if len(imageFiles) == 0 {
		return dto.ShipDeckImage{}, fmt.Errorf("no images were generated from PDF")
	}

	s3Path := fmt.Sprintf("ship_%s/regent-seven-seas/%s/deck_image", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)
	var uploadedS3Paths []string
	for i, imagePath := range imageFiles {
		service.Logger.Info().
			Int("imageIndex", i+1).
			Str("imagePath", imagePath).
			Str("shipUrlName", crawler.shipUrlName).
			Msg("Processing image")

		imageBytes, err := os.ReadFile(imagePath)
		if err != nil {
			service.Logger.Error().
				Err(err).
				Str("imagePath", imagePath).
				Str("shipUrlName", crawler.shipUrlName).
				Msg("Failed to read image file")
			continue
		}

		s3FileName := fmt.Sprintf("%s_%03d.jpg", crawler.shipUrlName, i+1)
		err = service.S3AddFile(imageBytes, s3FileName, s3Path)
		if err != nil {
			service.Logger.Error().
				Err(err).
				Str("s3FileName", s3FileName).
				Str("shipUrlName", crawler.shipUrlName).
				Msg("Failed to upload image to S3")
			continue
		}

		fullS3Path := fmt.Sprintf("%s/%s", s3Path, s3FileName)
		uploadedS3Paths = append(uploadedS3Paths, fullS3Path)
		service.Logger.Info().
			Str("s3Path", fullS3Path).
			Str("shipUrlName", crawler.shipUrlName).
			Msg("Successfully uploaded image to S3")
	}

	if len(uploadedS3Paths) == 0 {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to upload any images to S3")
	}

	err = os.RemoveAll(tempDir)
	if err != nil {
		service.Logger.Error().Err(err).Msgf("Failed to clean up temp directory: %s", tempDir)
	}

	service.Logger.Info().
		Int("totalUploaded", len(uploadedS3Paths)).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Successfully uploaded deck images to S3")

	// Return the first image path (main deck image)
	return dto.ShipDeckImage{
		ShipUrlName: crawler.shipUrlName,
		S3FilePath:  uploadedS3Paths[0],
	}, nil
}
