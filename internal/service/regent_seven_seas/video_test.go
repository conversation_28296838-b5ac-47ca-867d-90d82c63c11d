package regent_seven_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipVideo(t *testing.T) {
	shipVideoUrlMap := map[string]string{
		// "grandeur":  "https://www.rssc.com/ships/seven_seas_grandeur",
		"splendor": "https://www.rssc.com/ships/seven_seas_splendor",
		// "explorer":  "https://www.rssc.com/ships/seven_seas_explorer",
		// "voyager":   "https://www.rssc.com/ships/seven_seas_voyager",
		// "mariner":   "https://www.rssc.com/ships/seven_seas_mariner",
		// "navigator": "https://www.rssc.com/ships/seven_seas_navigator",
	}

	for shipUrlName, shipVideoUrl := range shipVideoUrlMap {
		crawler := NewCrawler(shipUrlName, true, 30*time.Second)

		coverMedia, err := crawler.CrawlShipVideo(shipVideoUrl)
		if err != nil {
			t.Fatalf("Failed to crawl Regent Seven Seas: %v", err)
		}

		fmt.Printf("Ship Cover Media: %+v\n", coverMedia)
	}
}
