package regent_seven_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipSuites(t *testing.T) {
	shipSuitesUrlMap := map[string]string{
		"grandeur":  "https://www.rssc.com/ships/seven_seas_grandeur",
		"splendor":  "https://www.rssc.com/ships/seven_seas_splendor",
		"explorer":  "https://www.rssc.com/ships/seven_seas_explorer",
		"voyager":   "https://www.rssc.com/ships/seven_seas_voyager",
		"mariner":   "https://www.rssc.com/ships/seven_seas_mariner",
		"navigator": "https://www.rssc.com/ships/seven_seas_navigator",
	}

	for shipUrlName, shipSuitesUrl := range shipSuitesUrlMap {
		crawler := NewCrawler(shipUrlName, true, 30*time.Second)

		suites, err := crawler.CrawlShipSuites(shipSuitesUrl)
		if err != nil {
			t.Fatalf("Failed to crawl Regent Seven Seas: %v", err)
		}

		for _, suite := range suites {
			fmt.Printf("Suite: %+v\n", suite)
		}
	}
}
