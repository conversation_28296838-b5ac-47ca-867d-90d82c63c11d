package regent_seven_seas

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

// CrawlShipCruises crawls all cruises for a specific ship
func (crawler *RegentSevenSeasCrawler) CrawlShipCruises(shipCruisesUrl string) (cruises []dto.ShipCruise, err error) {
	browser := service.LaunchBrowser(crawler.isHeadless)
	defer browser.MustClose()

	page := browser.MustPage()
	err = service.NavigateWithStatusCheck(page, shipCruisesUrl, crawler.timeout)
	if err != nil {
		return nil, err
	}

	service.ScrollPageDown(page, 5, 1500, 0)

	pageLinks, err := page.Elements("ul.pagination li.page-item a.page-link")
	if err != nil {
		service.Logger.Error().Err(err).Msg("unable to find page links")
	}

	totalPages := 1
	for _, link := range pageLinks {
		text, err := link.Text()
		if err != nil {
			continue
		}

		var pageNum int
		if _, err := fmt.Sscanf(text, "%d", &pageNum); err == nil {
			if pageNum > totalPages {
				totalPages = pageNum
			}
		}
	}

	for pageNum := 1; pageNum <= totalPages; pageNum++ {
		pageUrl := fmt.Sprintf("%s?pageNumber=%d", shipCruisesUrl, pageNum)
		service.Logger.Info().Msgf("Visiting page %d/%d: %s", pageNum, totalPages, pageUrl)

		page = browser.MustPage()
		err = service.NavigateWithStatusCheck(page, pageUrl, crawler.timeout)
		if err != nil {
			return nil, err
		}

		service.ScrollPageDown(page, 5, 1500, 1)

		items, err := page.Elements("ul.cruiseList li.cruiseList_item")
		if err != nil {
			service.Logger.Error().Err(err).Msg("unable to find cruise items")
			return cruises, err
		}

		for i, item := range items {
			cruise := dto.ShipCruise{}
			cruise.ShipUrlName = crawler.shipUrlName

			titleElem, err := item.Element(".cardText")
			if err != nil {
				service.Logger.Error().Err(err).Msgf("unable to find title element for cruise item %d", i+1)
				continue
			}
			cruise.Title = strings.TrimSpace(titleElem.MustText())

			fromElem, err := item.Element(".c89_header_title")
			if err != nil {
				service.Logger.Error().Err(err).Msgf("unable to find from element for cruise item %d", i+1)
				continue
			}
			cruise.From = strings.TrimSpace(fromElem.MustText())

			toElem, err := item.Element(".c89_header_subtitle")
			if err != nil {
				service.Logger.Error().Err(err).Msgf("unable to find to element for cruise item %d", i+1)
				continue
			}
			cruise.To = strings.TrimSpace(toElem.MustText())

			listingItems, err := item.Elements(".listing_item")
			if err != nil || len(listingItems) < 2 {
				service.Logger.Error().Err(err).Msgf("unable to find listing items for cruise item %d", i+1)
				continue
			}

			dateItems, err := listingItems[0].Elements(".e27_item")
			if err != nil || len(dateItems) < 3 {
				service.Logger.Error().Err(err).Msgf("unable to find date items for cruise item %d", i+1)
				continue
			}
			cruise.DepartDate = strings.TrimSpace(dateItems[1].MustText())
			cruise.DepartYear = strings.TrimSpace(dateItems[2].MustText())

			durationItems, err := listingItems[1].Elements(".e27_item")
			if err != nil || len(durationItems) < 2 {
				service.Logger.Error().Err(err).Msgf("unable to find duration items for cruise item %d", i+1)
				continue
			}
			cruise.Duration = strings.TrimSpace(durationItems[1].MustText())

			featureElem, err := item.Element(".e23_body")
			if err != nil {
				service.Logger.Warn().Err(err).Msgf("unable to find feature element for cruise item %d, setting empty", i+1)
				cruise.FeatureOffer = ""
			} else {
				cruise.FeatureOffer = strings.TrimSpace(featureElem.MustText())
			}

			viewFaresBtn, err := item.Element("a.e5.btn-primary[title='View Voyage & Fares']")
			if err != nil {
				viewFaresBtn, err = item.Element("a.btn-primary[href*='/cruises/']")
				if err != nil {
					service.Logger.Error().Err(err).Msgf("unable to find view fares button for cruise item %d", i+1)
					continue
				}
			}

			hrefAttr, err := viewFaresBtn.Attribute("href")
			if err != nil {
				service.Logger.Error().Err(err).Msgf("unable to find view fares button href attribute for cruise item %d", i+1)
				continue
			}

			baseUrl := strings.Split(*hrefAttr, "?")[0]
			cruise.DetailLink = "https://www.rssc.com" + baseUrl

			imageElement, err := item.Element("div.c329_figure_item.-image img.c5_figure_item.-desktop.visible-lg")
			if err == nil && imageElement != nil {
				srcAttr, err := imageElement.Attribute("src")
				if err == nil && srcAttr != nil {
					imageUrl := ""
					if strings.HasPrefix(*srcAttr, "/") {
						imageUrl = "https://www.rssc.com" + *srcAttr
					} else {
						imageUrl = *srcAttr
					}

					service.Logger.Info().
						Str("imageUrl", imageUrl).
						Str("title", cruise.Title).
						Msg("Found cruise image, fetching...")

					imageBytes, err := tool.FetchFileBytes(imageUrl)
					if err != nil {
						service.Logger.Error().
							Err(err).
							Str("imageUrl", imageUrl).
							Str("title", cruise.Title).
							Msg("Failed to fetch cruise image")
					} else {
						s3Path := fmt.Sprintf("ship_%s/regent-seven-seas/%s/cruises", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)
						cruiseName := fmt.Sprintf("%s_%s_%s_%s",
							strings.ToLower(strings.ReplaceAll(cruise.From, " ", "_")),
							strings.ToLower(strings.ReplaceAll(cruise.To, " ", "_")),
							cruise.DepartYear,
							convertDateFormat(cruise.DepartDate, cruise))
						s3FileName := fmt.Sprintf("%s.jpg", cruiseName)

						err = service.S3AddFile(imageBytes, s3FileName, s3Path)
						if err != nil {
							service.Logger.Error().
								Err(err).
								Str("s3FileName", s3FileName).
								Str("title", cruise.Title).
								Msg("Failed to upload cruise image to S3")
						} else {
							fullS3Path := fmt.Sprintf("%s/%s", s3Path, s3FileName)
							cruise.ImageS3Path = fullS3Path

							service.Logger.Info().
								Str("s3Path", fullS3Path).
								Str("title", cruise.Title).
								Msg("Successfully uploaded cruise image to S3")
						}
					}
				}
			}

			// cruise.Price, cruise.IsWaitlisted 改由 itinerary 取得，避免額外開 page 造成資源跟時間浪費

			cruises = append(cruises, cruise)
			service.Logger.Info().Msgf("Page %d processing cruise #%d", pageNum, i+1)
		}
	}

	return cruises, nil
}

func convertDateFormat(dateStr string, cruise dto.ShipCruise) string {
	monthMap := map[string]string{
		"JAN": "01", "FEB": "02", "MAR": "03", "APR": "04",
		"MAY": "05", "JUN": "06", "JUL": "07", "AUG": "08",
		"SEP": "09", "OCT": "10", "NOV": "11", "DEC": "12",
	}

	re := regexp.MustCompile(`^([A-Z]{3})\s+(\d{1,2})$`)
	matches := re.FindStringSubmatch(strings.ToUpper(dateStr))

	if len(matches) != 3 {
		service.Logger.Error().Msgf("Invalid date format '%s' for cruise '%s', expected format like 'JUN 20'", dateStr, cruise.Title)
		return strings.ReplaceAll(dateStr, "/", "_")
	}

	monthAbbr := matches[1]
	dayStr := matches[2]

	monthNum, exists := monthMap[monthAbbr]
	if !exists {
		service.Logger.Error().Msgf("Invalid month abbreviation '%s' for cruise '%s'", monthAbbr, cruise.Title)
		return strings.ReplaceAll(dateStr, "/", "_")
	}

	day, err := strconv.Atoi(dayStr)
	if err != nil {
		service.Logger.Error().Msgf("Invalid day '%s' for cruise '%s': %v", dayStr, cruise.Title, err)
		return strings.ReplaceAll(dateStr, "/", "_")
	}

	if day < 1 || day > 31 {
		service.Logger.Error().Msgf("Invalid day %d for cruise '%s'", day, cruise.Title)
		return strings.ReplaceAll(dateStr, "/", "_")
	}

	return fmt.Sprintf("%s_%02d", monthNum, day)
}
