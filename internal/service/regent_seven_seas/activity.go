package regent_seven_seas

import (
	"fmt"
	"strings"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

func (crawler *RegentSevenSeasCrawler) CrawlShipActivity(url string) (activities []dto.ShipActivity, err error) {
	page, teardown, err := crawler.setupPage(url)
	if err != nil {
		return nil, err
	}
	defer teardown()

	service.ScrollPageDown(page, 5, 1500, 0)

	firstCruiseCard, err := page.Element("ul.cruiseList li.cruiseList_item:first-child")
	if err != nil {
		service.Logger.Error().Err(err).Msg("unable to find first cruise card")
		return nil, err
	}

	detailLink, err := firstCruiseCard.Element("a.e5.btn-primary[title='View Voyage & Fares']")
	if err != nil {
		service.Logger.Error().Err(err).Msg("unable to find detail link in first cruise card")
		return nil, err
	}

	href, err := detailLink.Attribute("href")
	if err != nil {
		service.Logger.Error().Err(err).Msg("unable to get href attribute")
		return nil, err
	}

	baseUrl := strings.Split(*href, "?")[0]
	firstCruiseDetailLink := "https://www.rssc.com" + baseUrl
	activities, err = crawler.CrawlShipCruiseOnboardActivities(firstCruiseDetailLink)
	if err != nil {
		service.Logger.Error().Err(err).Msg("failed to crawl onboard activities from cruise detail page")
		return nil, err
	}

	return activities, nil
}

func (crawler *RegentSevenSeasCrawler) CrawlShipCruiseOnboardActivities(cruiseDetailUrl string) (activities []dto.ShipActivity, err error) {
	page, teardown, err := crawler.setupPage(cruiseDetailUrl)
	if err != nil {
		return nil, err
	}
	defer teardown()

	service.ScrollPageDown(page, 5, 1500, 0)
	time.Sleep(1 * time.Second)

	activitiesSection, err := page.Element(".m76_content")
	if err != nil {
		return nil, fmt.Errorf("failed to find onboard activities section: %w", err)
	}

	activityItems, err := activitiesSection.Elements(".listing_item")
	if err != nil {
		return nil, fmt.Errorf("failed to find activity items: %w", err)
	}

	for _, item := range activityItems {
		activity := dto.ShipActivity{
			ShipUrlName: crawler.shipUrlName,
		}

		titleElement, err := item.Element(".headline.-subtitle")
		if err == nil && titleElement != nil {
			activity.Title, _ = titleElement.Text()
		}

		contentElement, err := item.Element(".paragraph.-small")
		if err == nil && contentElement != nil {
			activity.Content, _ = contentElement.Text()
		}

		imageElement, err := item.Element(".c5_figure_item.-desktop")
		if err == nil && imageElement != nil {
			srcAttr, err := imageElement.Attribute("src")
			if err == nil && srcAttr != nil {
				imageUrl := *srcAttr

				if strings.HasPrefix(imageUrl, "/") {
					imageUrl = "https://www.rssc.com" + imageUrl
				}

				imageBytes, err := tool.FetchFileBytes(imageUrl)
				if err != nil {
					service.Logger.Error().
						Err(err).
						Str("imageUrl", imageUrl).
						Str("title", activity.Title).
						Msg("Failed to fetch Regent Seven Seas activity image")
				} else {
					s3Path := fmt.Sprintf("ship_%s/regent-seven-seas/%s/activities", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)

					cleanTitle := strings.ToLower(strings.ReplaceAll(activity.Title, " ", "_"))
					cleanTitle = strings.ReplaceAll(cleanTitle, "&", "and")
					s3FileName := fmt.Sprintf("activity_%s.jpg", cleanTitle)

					err = service.S3AddFile(imageBytes, s3FileName, s3Path)
					if err != nil {
						service.Logger.Error().
							Err(err).
							Str("s3FileName", s3FileName).
							Str("title", activity.Title).
							Msg("Failed to upload Regent Seven Seas activity image to S3")
					} else {
						fullS3Path := fmt.Sprintf("%s/%s", s3Path, s3FileName)
						activity.ImageS3Path = fullS3Path

						service.Logger.Info().
							Str("s3Path", fullS3Path).
							Str("title", activity.Title).
							Msg("Successfully uploaded Regent Seven Seas activity image to S3")
					}
				}
			}
		}

		if activity.Title != "" {
			activities = append(activities, activity)
		}
	}

	service.Logger.Info().
		Int("totalActivities", len(activities)).
		Msg("Completed Regent Seven Seas onboard activities crawling")

	return activities, nil
}
