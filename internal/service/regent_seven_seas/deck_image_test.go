package regent_seven_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipDeckImages(t *testing.T) {
	shipDeckImageUrlMap := map[string]string{
		"grandeur":  "https://www.rssc.com/ships/explorer?ship=GRA",
		"splendor":  "https://www.rssc.com/ships/explorer?ship=SPL",
		"explorer":  "https://www.rssc.com/ships/explorer?ship=EXP",
		"voyager":   "https://www.rssc.com/ships/explorer?ship=VOY",
		"mariner":   "https://www.rssc.com/ships/explorer?ship=MAR",
		"navigator": "https://www.rssc.com/ships/explorer?ship=NAV",
	}

	for shipUrlName, shipDeckImageUrl := range shipDeckImageUrlMap {
		crawler := NewCrawler(shipUrlName, true, 30*time.Second)

		deckImage, err := crawler.CrawlShipDeckImages(shipDeckImageUrl)
		if err != nil {
			t.Fatalf("Failed to crawl Regent Seven Seas: %v", err)
		}

		fmt.Printf("Ship Deck Image: %+v\n", deckImage)
	}
}
