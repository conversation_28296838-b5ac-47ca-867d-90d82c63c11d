package regent_seven_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipSpec(t *testing.T) {
	shipURLMap := map[string]string{
		"grandeur":  "https://www.rssc.com/ships/seven_seas_grandeur",
		"splendor":  "https://www.rssc.com/ships/seven_seas_splendor",
		"explorer":  "https://www.rssc.com/ships/seven_seas_explorer",
		"voyager":   "https://www.rssc.com/ships/seven_seas_voyager",
		"mariner":   "https://www.rssc.com/ships/seven_seas_mariner",
		"navigator": "https://www.rssc.com/ships/seven_seas_navigator",
	}

	for shipUrlName, url := range shipURLMap {
		crawler := NewCrawler(shipUrlName, true, 30*time.Second)

		spec, err := crawler.CrawlShipSpec(url)
		if err != nil {
			t.Errorf("Error crawling %s: %v", shipUrlName, err)
			continue
		}

		fmt.Printf("spec: %+v\n", spec)
	}
}
