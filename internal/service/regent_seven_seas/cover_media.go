package regent_seven_seas

import (
	"fmt"
	"strings"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

// CrawlShipCoverMedia crawls and downloads ship cover media (videos or images)
func (crawler *RegentSevenSeasCrawler) CrawlShipCoverMedia(shipCoverMediaUrl string) (coverMedia dto.ShipCoverMedia, err error) {
	page, teardown, err := crawler.setupPage(shipCoverMediaUrl)
	if err != nil {
		return coverMedia, err
	}
	defer teardown()

	service.ScrollPageDown(page, 3, 1000, 1)

	heroImageSelector := "figure img[alt*=\"Seven Seas\"]"
	heroImageElement, err := page.Element(heroImageSelector)
	if err != nil {
		heroImageSelector = "main figure img"
		heroImageElement, err = page.Element(heroImageSelector)
		if err != nil {
			return coverMedia, fmt.Errorf("cannot find hero image: %w", err)
		}
	}

	srcAttr, err := heroImageElement.Attribute("src")
	if err != nil || srcAttr == nil {
		return coverMedia, fmt.Errorf("cannot get image src attribute: %w", err)
	}

	imageURL := *srcAttr
	if strings.HasPrefix(imageURL, "/") {
		imageURL = "https://www.rssc.com" + imageURL
	}

	imageBytes, err := tool.FetchFileBytes(imageURL)
	if err != nil {
		return coverMedia, fmt.Errorf("failed to download cover image: %w", err)
	}

	s3Path := fmt.Sprintf("ship_%s/regent-seven-seas/%s/cover_media", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)
	fileName := fmt.Sprintf("cover_%s.jpg", crawler.shipUrlName)

	err = service.S3AddFile(imageBytes, fileName, s3Path)
	if err != nil {
		service.Logger.Error().
			Err(err).
			Str("s3FileName", fileName).
			Str("shipUrlName", crawler.shipUrlName).
			Msg("Failed to upload cover image to S3")
		return coverMedia, fmt.Errorf("failed to upload image to S3: %w", err)
	}

	s3FullPath := fmt.Sprintf("%s/%s", s3Path, fileName)
	service.Logger.Info().
		Str("s3Path", s3FullPath).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Successfully uploaded cover image to S3")

	coverMedia.ShipUrlName = crawler.shipUrlName
	coverMedia.S3FilePath = s3FullPath

	return coverMedia, nil
}
