package regent_seven_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipActivity(t *testing.T) {
	shipActivityUrlMap := map[string]string{
		"grandeur": "https://www.rssc.com/ships/seven_seas_grandeur",
		// "splendor":  "https://www.rssc.com/ships/seven_seas_splendor",
		// "explorer":  "https://www.rssc.com/ships/seven_seas_explorer",
		// "voyager":   "https://www.rssc.com/ships/seven_seas_voyager",
		// "mariner":   "https://www.rssc.com/ships/seven_seas_mariner",
		// "navigator": "https://www.rssc.com/ships/seven_seas_navigator",
	}

	for shipUrlName, shipActivityUrl := range shipActivityUrlMap {
		crawler := NewCrawler(shipUrlName, true, 60*time.Second)

		activities, err := crawler.CrawlShipActivity(shipActivityUrl)
		if err != nil {
			fmt.Printf("Failed to crawl activities for %s: %v", shipUrlName, err)
		}

		for _, activity := range activities {
			fmt.Printf("Activity: %+v\n", activity)
		}
	}
}
