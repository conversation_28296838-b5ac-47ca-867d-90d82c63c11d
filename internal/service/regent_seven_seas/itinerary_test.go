package regent_seven_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipItineraries(t *testing.T) {
	testCases := []struct {
		shipUrlName string
		url         string
		from        string
		to          string
	}{
		{"grandeur", "https://www.rssc.com/cruises/GRA260125/summary", "Test From", "Test To"},
		{"grandeur", "https://www.rssc.com/cruises/GRA250727/summary", "Test From", "Test To"},
		{"grandeur", "https://www.rssc.com/cruises/GRA251025/summary", "Test From", "Test To"},
		{"explorer", "https://www.rssc.com/cruises/EXP261229/summary", "Test From", "Test To"},
	}

	for _, tc := range testCases {
		crawler := NewCrawler(tc.shipUrlName, true, 30*time.Second)

		itinerary, isWaitlisted, price, err := crawler.CrawlShipItineraries(tc.url, tc.from, tc.to)
		if err != nil {
			t.Fatalf("Failed to crawl Regent Seven Seas: %v", err)
		}

		fmt.Printf("Is Waitlisted: %t\n", isWaitlisted)
		fmt.Printf("Price: %s\n", price)

		fmt.Printf("Itinerary Count for %s: %d\n", tc.shipUrlName, len(itinerary))
		for i, item := range itinerary {
			fmt.Printf("Itinerary[%d]: %+v\n", i, item)
		}
	}
}
