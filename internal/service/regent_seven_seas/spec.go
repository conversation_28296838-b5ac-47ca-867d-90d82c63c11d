package regent_seven_seas

import (
	"strings"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
)

// CrawlShipSpec crawls ship specifications from the given Url
func (crawler *RegentSevenSeasCrawler) CrawlShipSpec(specUrl string) (spec dto.ShipSpec, err error) {
	page, teardown, err := crawler.setupPage(specUrl)
	if err != nil {
		return spec, err
	}
	defer teardown()

	listItems := page.MustElements("div.c121 > ul.listing > li.listing_item")

	for _, item := range listItems {
		label := item.MustElement("span.label").MustText()
		value := item.MustElement("span.numbers").MustText()

		// Store data based on label
		switch strings.ToUpper(label) {
		case "GUESTS":
			spec.Guests = value
		case "CREW":
			spec.Crew = value
		case "SUITES":
			spec.Suites = value
		case "DECKS":
			spec.Decks = value
		case "LENGTH":
			spec.Length = value
		case "WIDTH":
			spec.Width = value
		case "TONNAGE":
			spec.Tonnage = value
		}
	}

	spec.ShipUrlName = crawler.shipUrlName

	return spec, nil
}
