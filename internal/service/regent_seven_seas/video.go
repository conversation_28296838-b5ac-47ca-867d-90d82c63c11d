package regent_seven_seas

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

func (crawler *RegentSevenSeasCrawler) CrawlShipVideo(shipVideoUrl string) (video dto.ShipVideo, err error) {
	page, teardown, err := crawler.setupPage(shipVideoUrl)
	if err != nil {
		return video, err
	}
	defer teardown()

	service.ScrollPageDown(page, 5, 1500, 2)

	videoBtn := page.MustElement(`a[href="#modal-1"]`)
	videoBtn.MustClick()

	time.Sleep(2 * time.Second)

	iframe, err := page.Element(`#modal-1 iframe`)
	if err != nil {
		return video, fmt.Errorf("cannot find video iframe: %w", err)
	}

	videoSrc, err := iframe.Attribute("src")
	if err != nil || videoSrc == nil {
		return video, fmt.Errorf("unable to get video URL: %w", err)
	}

	var videoID string
	parts := strings.Split(*videoSrc, "/")
	for i, part := range parts {
		if part == "embed" && i < len(parts)-1 {
			videoID = strings.Split(parts[i+1], "?")[0]
			break
		}
	}

	videoUrl := fmt.Sprintf("https://www.youtube.com/watch?v=%s", videoID)
	service.Logger.Info().
		Str("videoUrl", videoUrl).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Found video URL, starting download")

	tempDir := filepath.Join("temp", "videos", crawler.shipUrlName)
	err = os.MkdirAll(tempDir, 0755)
	if err != nil {
		return video, fmt.Errorf("failed to create temp directory: %w", err)
	}

	fileName := fmt.Sprintf("video_%s.mp4", crawler.shipUrlName)
	tempPath := filepath.Join(tempDir, fileName)

	cmd := exec.Command("yt-dlp",
		"-f", "bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best",
		"-o", tempPath,
		"--quiet",
		"--no-warnings",
		videoUrl,
	)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return video, fmt.Errorf("yt-dlp failed: %w, output: %s", err, string(output))
	}

	fileBytes, err := os.ReadFile(tempPath)
	if err != nil {
		return video, fmt.Errorf("failed to read downloaded video file: %w", err)
	}

	s3Path := fmt.Sprintf("ship_%s/regent-seven-seas/%s/video", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)
	err = service.S3AddFile(fileBytes, fileName, s3Path)
	if err != nil {
		service.Logger.Error().
			Err(err).
			Str("s3FileName", fileName).
			Str("shipUrlName", crawler.shipUrlName).
			Msg("Failed to upload video to S3")
		return video, fmt.Errorf("failed to upload video to S3: %w", err)
	}

	err = os.RemoveAll(tempDir)
	if err != nil {
		service.Logger.Error().Err(err).Msgf("Failed to clean up temp directory: %s", tempDir)
	}

	s3FullPath := fmt.Sprintf("%s/%s", s3Path, fileName)
	service.Logger.Info().
		Str("s3Path", s3FullPath).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Successfully uploaded video to S3")

	video.ShipUrlName = crawler.shipUrlName
	video.S3FilePath = s3FullPath

	return video, nil
}
