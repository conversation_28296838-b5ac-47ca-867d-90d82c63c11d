package service

import (
	"context"
	"errors"
	"fmt"
	"os"
	"time"

	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/launcher"
	"github.com/go-rod/rod/lib/proto"
)

func LaunchBrowser(isHeadless bool) *rod.Browser {
	// Check if we're running in Docker, use the installed Chrome binary
	path := os.Getenv("CHROME_BIN")
	if path != "" {
		u := launcher.New().Bin(path).
			Set("no-sandbox", "").
			Set("disable-dev-shm-usage", "").
			Set("disable-gpu", "").
			Headless(isHeadless).MustLaunch()
		return rod.New().ControlURL(u).MustConnect()
	}

	// Otherwise, let go-rod download and use its own Chrome
	url := launcher.New().Headless(isHeadless).MustLaunch()
	return rod.New().ControlURL(url).MustConnect()
}

// NavigateWithStatusCheck navigates to a URL and checks the HTTP status code
// Returns error if navigation fails, timeout occurs, or 403 status is received
func NavigateWithStatusCheck(page *rod.Page, targetUrl string, timeout time.Duration) error {
	var statusCode int
	done := make(chan bool)

	go func() {
		page.WaitEvent(&proto.NetworkResponseReceived{})
		page.EachEvent(func(e *proto.NetworkResponseReceived) bool {
			if e.Type == proto.NetworkResourceTypeDocument && e.Response.URL == targetUrl {
				statusCode = e.Response.Status
				done <- true
				return true
			}
			return false
		})
	}()

	err := page.Timeout(timeout).Navigate(targetUrl)
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) {
			return fmt.Errorf("timeout while navigating to URL")
		}
		return fmt.Errorf("failed to navigate to URL: %w", err)
	}

	page.MustWaitLoad()

	select {
	case <-done:
		if statusCode == 403 {
			return fmt.Errorf("HTTP 403 Forbidden")
		}
	case <-time.After(5 * time.Second):
	}

	return nil
}

func ScrollPageDown(page *rod.Page, numScrolls int, scrollPixels int, delaySeconds int) {
	for i := 0; i < numScrolls; i++ {
		err := page.Mouse.Scroll(0, float64(scrollPixels), 3)
		if err != nil {
			Logger.Error().Err(err).Msg("error scrolling page")
			break
		}
	}

	if delaySeconds > 0 {
		time.Sleep(time.Duration(delaySeconds) * time.Second)
	}
}
