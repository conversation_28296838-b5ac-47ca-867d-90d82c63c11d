package service

import (
	"bytes"
	"net/http"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

var awsSession *session.Session

func init() {
	if conf.Config.S3Region == "" || conf.Config.S3AccessID == "" || conf.Config.S3AccessKey == "" || conf.Config.S3Bucket == "" {
		return
	}

	s, err := session.NewSession(&aws.Config{
		Region:      aws.String(conf.Config.S3Region),
		Credentials: credentials.NewStaticCredentials(conf.Config.S3AccessID, conf.Config.S3AccessKey, ""),
	})
	if err != nil {
		Logger.Error().Msg(err.Error())
		return
	}

	awsSession = s
}

func S3AddFile(fileBytes []byte, s3FileName string, s3ImagePath string) error {
	_, err := s3.New(awsSession).PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(conf.Config.S3Bucket),
		Key:         aws.String(s3ImagePath + "/" + s3FileName),
		ACL:         aws.String("public-read"),
		Body:        bytes.NewReader(fileBytes),
		ContentType: aws.String(http.DetectContentType(fileBytes)),
	})

	return err
}
