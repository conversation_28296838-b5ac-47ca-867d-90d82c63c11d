package silver_seas

import (
	"strconv"
	"strings"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
	"github.com/go-rod/rod"
)

type SilverSeasCrawler struct {
	shipUrlName string
	isHeadless  bool
	timeout     time.Duration
}

func NewCrawler(shipUrlName string, isHeadless bool, timeout time.Duration) *SilverSeasCrawler {
	return &SilverSeasCrawler{
		shipUrlName: shipUrlName,
		isHeadless:  isHeadless,
		timeout:     timeout,
	}
}

func (crawler *SilverSeasCrawler) setupPage(url string) (page *rod.Page, teardown func(), err error) {
	browser := service.LaunchBrowser(crawler.isHeadless)
	teardown = func() {
		browser.MustClose()
	}

	page = browser.MustPage()
	err = service.NavigateWithStatusCheck(page, url, crawler.timeout)
	if err != nil {
		return nil, teardown, err
	}

	return page, teardown, nil
}

// getHighestResolutionImageFromSrcset extracts the highest resolution image URL from srcset attribute
// Returns the URL with the highest width value, or the src attribute if srcset is empty
func getHighestResolutionImageFromSrcset(srcset, src string) string {
	if srcset == "" {
		return src
	}

	sources := strings.Split(srcset, ", ")
	var bestUrl string
	var maxWidth int

	for _, source := range sources {
		source = strings.TrimSpace(source)

		lastSpaceIndex := strings.LastIndex(source, " ")
		if lastSpaceIndex == -1 {
			continue
		}

		url := strings.TrimSpace(source[:lastSpaceIndex])
		widthStr := strings.TrimSpace(source[lastSpaceIndex+1:])

		if strings.HasSuffix(widthStr, "w") {
			widthStr = strings.TrimSuffix(widthStr, "w")
			if width, err := strconv.Atoi(widthStr); err == nil {
				if width > maxWidth {
					maxWidth = width
					bestUrl = url
				}
			}
		}
	}

	if bestUrl == "" {
		return src
	}

	return bestUrl
}
