package silver_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipActivity(t *testing.T) {
	shipActivityUrlMap := map[string]string{
		"dawn": "https://www.silversea.com/ships/silver-dawn.html",
		"ray":  "https://www.silversea.com/ships/silver-ray.html",
	}

	for shipUrlName, shipActivityUrl := range shipActivityUrlMap {
		crawler := NewCrawler(shipUrlName, true, 30*time.Second)

		activities, err := crawler.CrawlShipActivity(shipActivityUrl)
		if err != nil {
			fmt.Printf("Failed to crawl ship activities: %v", err)
		}

		for i, activity := range activities {
			fmt.Printf("Activity %d: %+v\n", i+1, activity)
		}
	}
}
