package silver_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipItineraries(t *testing.T) {
	testCases := []struct {
		shipUrlName string
		url         string
		from        string
		to          string
	}{
		{"dawn", "https://www.silversea.com/destinations/mediterranean-cruise/lisbon-to-lisbon-da251126012.html", "Lisbon", "Lisbon"},
	}

	for _, tc := range testCases {
		crawler := NewCrawler(tc.shipUrlName, false, 30*time.Second)

		itinerary, mapViewS3Path, err := crawler.CrawlShipItineraries(tc.url, tc.from, tc.to)
		if err != nil {
			t.Fatalf("Failed to crawl Silver Seas itineraries: %v", err)
		}

		for i, item := range itinerary {
			fmt.Printf("Itinerary[%d]: %+v\n", i, item)
		}
		fmt.Printf("Map View S3 Path: %s\n", mapViewS3Path)
	}
}
