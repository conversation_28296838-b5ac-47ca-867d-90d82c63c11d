package silver_seas

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/input"
	"github.com/go-rod/rod/lib/proto"
)

// CrawlShipSuites crawls suite information for a ship
func (crawler *SilverSeasCrawler) CrawlShipSuites(shipSuitesUrl string) (suites []dto.ShipSuite, err error) {
	page, teardown, err := crawler.setupPage(shipSuitesUrl)
	if err != nil {
		return nil, err
	}
	defer teardown()

	service.ScrollPageDown(page, 3, 1500, 1)

	swiperSlides, err := crawler.findSuitesSection(page)
	if err != nil {
		return nil, err
	}

	for i, slide := range swiperSlides {
		suite, err := crawler.processSwipeSlide(page, slide, i)
		if err != nil {
			service.Logger.Warn().
				Err(err).
				Int("slideIndex", i).
				Msg("Failed to process swiper slide, continuing with next")
			continue
		}

		if suite != nil {
			suites = append(suites, *suite)
		}
	}

	service.Logger.Info().
		Int("totalSuites", len(suites)).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Successfully crawled all ship suites")

	return suites, nil
}

func (crawler *SilverSeasCrawler) findSuitesSection(page *rod.Page) ([]*rod.Element, error) {
	selector := `[class*="SectionContainer-module_background"][data-content-name="suites"]`

	suitesSection, err := page.Element(selector)
	if err != nil {
		return nil, fmt.Errorf("cannot find suites section with selector %s: %w", selector, err)
	}

	swiperSlides, err := suitesSection.Elements(".swiper-slide")
	if err != nil {
		return nil, fmt.Errorf("cannot find swiper-slide elements: %w", err)
	}

	if len(swiperSlides) == 0 {
		return nil, fmt.Errorf("no swiper-slide elements found in suites section")
	}

	return swiperSlides, nil
}

func (crawler *SilverSeasCrawler) processSwipeSlide(page *rod.Page, slide *rod.Element, slideIndex int) (*dto.ShipSuite, error) {
	// "Discover more" button
	discoverMoreBtn, err := slide.Element(`button[data-content-piece="more_button"]`)
	if err != nil {
		return nil, fmt.Errorf("cannot find discover more button in slide %d: %w", slideIndex, err)
	}

	visible, err := discoverMoreBtn.Visible()
	if err != nil || !visible {
		return nil, fmt.Errorf("discover more button is not visible in slide %d", slideIndex)
	}

	_ = discoverMoreBtn.ScrollIntoView()
	time.Sleep(300 * time.Millisecond)

	err = discoverMoreBtn.Click(proto.InputMouseButtonLeft, 1)
	if err != nil {
		return nil, fmt.Errorf("failed to click discover more button in slide %d: %w", slideIndex, err)
	}

	modal, err := page.Timeout(5 * time.Second).Element("[data-content-name*='modal_'][data-content-name*='suite']")
	if err != nil {
		return nil, fmt.Errorf("modal not found or not visible after trying all selectors")
	}

	err = modal.WaitVisible()
	if err != nil {
		return nil, fmt.Errorf("modal not found or not visible after trying all selectors")
	}
	time.Sleep(500 * time.Millisecond)

	title, description, err := crawler.extractOverview(modal)
	if err != nil {
		return nil, fmt.Errorf("failed to extract modal content for slide %d: %w", slideIndex, err)
	}

	amenities, err := crawler.extractAmenities(modal)
	if err != nil {
		service.Logger.Warn().
			Err(err).
			Msg("Failed to extract amenities from modal, continuing without amenities")
		amenities = ""
	}

	// Extract decks from amenities
	decks := crawler.extractDecksFromAmenities(amenities)

	suiteSize, balconySize := crawler.extractSuiteSizeAndBalconySize(modal)

	categoryDetails := []dto.CategoryDetail{
		{
			Category:    "",
			SuiteSize:   suiteSize,
			BalconySize: balconySize,
			Decks:       decks,
		},
	}

	suite := &dto.ShipSuite{
		ShipUrlName:     crawler.shipUrlName,
		SuiteName:       title,
		Overview:        description,
		Amenities:       amenities,
		CategoryDetails: categoryDetails,
	}

	s3ImagePath, err := crawler.extractImage(modal, title)
	if err != nil {
		service.Logger.Warn().
			Err(err).
			Msg("Failed to extract and upload image, continuing without image")
	} else if s3ImagePath != "" {
		suite.ImageS3Paths = [][]string{{s3ImagePath}}
	}

	_ = page.Keyboard.Press(input.Escape)
	time.Sleep(500 * time.Millisecond)

	return suite, nil
}

func (crawler *SilverSeasCrawler) extractOverview(modal *rod.Element) (title string, description string, err error) {
	titleElement, err := modal.Element("h3")
	if err != nil {
		return "", "", fmt.Errorf("cannot find title element in modal: %w", err)
	}

	title, err = titleElement.Text()
	if err != nil {
		return "", "", fmt.Errorf("cannot get title from title element")
	}

	descriptionElement, err := modal.Element("p")
	if err != nil {
		return "", "", fmt.Errorf("cannot find description element in modal: %w", err)
	}

	description, err = descriptionElement.Text()
	if err != nil {
		return "", "", fmt.Errorf("cannot get description from description element")
	}

	return title, description, nil
}

func (crawler *SilverSeasCrawler) extractAmenities(modal *rod.Element) (string, error) {
	tabs, err := modal.Elements(`[role="tab"]`)
	if err != nil {
		return "", fmt.Errorf("cannot find tabs in modal: %w", err)
	}

	var suiteFeaturesTab *rod.Element
	for _, tab := range tabs {
		tabText, err := tab.Text()
		if err == nil && strings.Contains(tabText, "SUITE FEATURES") {
			suiteFeaturesTab = tab
			service.Logger.Info().
				Str("tabText", tabText).
				Msg("Found Suite Features tab")
			break
		}
	}

	if suiteFeaturesTab == nil {
		return "", fmt.Errorf("cannot find Suite Features tab")
	}

	selected, _ := suiteFeaturesTab.Attribute("aria-selected")
	if selected == nil || *selected != "true" {
		_, err := suiteFeaturesTab.Eval("() => this.click()")
		if err != nil {
			return "", fmt.Errorf("failed to click Suite Features tab: %w", err)
		}

		time.Sleep(1 * time.Second)
	}

	amenitiesText, err := crawler.extractStructuredAmenities(modal)
	if err != nil {
		return "", fmt.Errorf("cannot extract structured amenities: %w", err)
	}

	return amenitiesText, nil
}

func (crawler *SilverSeasCrawler) extractStructuredAmenities(modal *rod.Element) (string, error) {
	groups, err := modal.Elements(".ShipFacilityModal-module--group--c2062")
	if err != nil {
		return "", fmt.Errorf("cannot find amenity groups: %w", err)
	}

	var amenitiesBuilder strings.Builder
	for i, group := range groups {
		categoryTitle, err := group.Element("h3")
		if err != nil {
			continue
		}

		categoryText, err := categoryTitle.Text()
		if err != nil {
			continue
		}

		if i > 0 {
			amenitiesBuilder.WriteString("\n")
		}
		amenitiesBuilder.WriteString(fmt.Sprintf("**%s:**\n", categoryText))

		listItems, err := group.Elements("li")
		if err != nil {
			continue
		}

		for _, item := range listItems {
			itemText, err := item.Text()
			if err != nil {
				continue
			}
			amenitiesBuilder.WriteString(fmt.Sprintf("- %s\n", itemText))
		}
	}

	result := amenitiesBuilder.String()
	if result == "" {
		return "", fmt.Errorf("no amenities data extracted")
	}

	service.Logger.Info().
		Str("extractedAmenities", result).
		Msg("Successfully extracted structured amenities")

	return result, nil
}

func (crawler *SilverSeasCrawler) extractDecksFromAmenities(amenities string) string {
	// Look for pattern like "- Deck(s): 4" or "- Deck: 4"
	deckPattern := regexp.MustCompile(`(?i)-\s*Deck\(s\)?:\s*(\d+(?:,\s*\d+)*)`)
	matches := deckPattern.FindStringSubmatch(amenities)

	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	return ""
}

func (crawler *SilverSeasCrawler) extractSuiteSizeAndBalconySize(modal *rod.Element) (suiteSize string, balconySize string) {
	bedroomInfoDiv, err := modal.Element(".ShipFacilityModal-module--bedrooms-info--14389")
	if err != nil {
		service.Logger.Info().Msg("Cannot find bedroom info div with the specific class")
		return
	}

	pElement, err := bedroomInfoDiv.Element("p")
	if err != nil {
		service.Logger.Info().Msg("Cannot find p element within bedroom info div")
		return
	}

	sizeText, err := pElement.Text()
	if err != nil {
		service.Logger.Info().Msg("Cannot get text from p element")
		return
	}

	// One bedroom: 73 sq m including veranda (12 sq m)
	sizeTextSplit := strings.Split(sizeText, "sq")
	sizes := []string{}
	for _, size := range sizeTextSplit {
		matches := regexp.MustCompile(`\d+`).FindAllString(size, -1)
		if len(matches) > 0 {
			sizes = append(sizes, strings.Join(matches, "-"))
		}
	}

	if len(sizes) > 0 && len(sizes) < 3 {
		if len(sizes) == 1 {
			suiteSize = sizes[0]
		} else {
			suiteSize = sizes[0]
			balconySize = sizes[1]
		}
	}

	return suiteSize, balconySize
}

func (crawler *SilverSeasCrawler) extractImage(modal *rod.Element, suiteName string) (imageS3Path string, err error) {
	tabs, err := modal.Elements(`[role="tab"]`)
	if err != nil {
		return "", fmt.Errorf("cannot find tabs in modal: %w", err)
	}

	var overviewTab *rod.Element
	for _, tab := range tabs {
		tabText, err := tab.Text()
		if err == nil && (strings.Contains(tabText, "SUITE OVERVIEW") || strings.Contains(tabText, "OVERVIEW")) {
			overviewTab = tab
			service.Logger.Info().
				Str("tabText", tabText).
				Msg("Found Overview tab")
			break
		}
	}

	if overviewTab != nil {
		selected, _ := overviewTab.Attribute("aria-selected")
		if selected == nil || *selected != "true" {
			_, err := overviewTab.Eval("() => this.click()")
			if err != nil {
				return "", fmt.Errorf("failed to click Overview tab: %w", err)
			}
			time.Sleep(1 * time.Second)
		}
	}

	overviewSelectors := []string{
		"[role='tabpanel'][aria-hidden='false'] img",
		"[role='tabpanel']:not([aria-hidden='true']) img",
	}

	var imgElement *rod.Element
	for _, selector := range overviewSelectors {
		imgElements, err := modal.Elements(selector)
		if err == nil && len(imgElements) > 0 {
			for _, img := range imgElements {
				visible, _ := img.Visible()
				if visible {
					imgElement = img
					break
				}
			}
			if imgElement != nil {
				break
			}
		}
	}

	if imgElement == nil {
		return "", fmt.Errorf("cannot find any visible image elements in overview tab")
	}

	srcset, err := imgElement.Attribute("srcset")
	var srcsetValue string
	if err == nil && srcset != nil {
		srcsetValue = *srcset
	}

	src, err := imgElement.Attribute("src")
	if err != nil || src == nil || *src == "" {
		return "", fmt.Errorf("cannot get src attribute from image")
	}
	srcValue := *src

	imageUrl := getHighestResolutionImageFromSrcset(srcsetValue, srcValue)
	imageBytes, err := tool.FetchFileBytes(imageUrl)
	if err != nil {
		return "", fmt.Errorf("failed to download image from %s: %w", imageUrl, err)
	}

	s3Path := fmt.Sprintf("ship_%s/silver-seas/%s/suites", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)
	s3FileName := fmt.Sprintf("suite_%s.jpg", strings.ToLower(strings.ReplaceAll(suiteName, " ", "_")))

	err = service.S3AddFile(imageBytes, s3FileName, s3Path)
	if err != nil {
		return "", fmt.Errorf("failed to upload image to S3: %w", err)
	}

	imageS3Path = fmt.Sprintf("%s/%s", s3Path, s3FileName)

	service.Logger.Info().
		Str("s3Path", imageS3Path).
		Str("suiteName", suiteName).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Successfully uploaded suite image to S3")

	return imageS3Path, nil
}
