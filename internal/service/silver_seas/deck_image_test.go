package silver_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipDeckImages(t *testing.T) {
	shipDeckImageUrls := map[string]string{
		"dawn": "https://www.silversea.com/ships/silver-dawn.html",
		"ray":  "https://www.silversea.com/ships/silver-ray.html",
	}

	for shipUrlName, shipDeckImageUrl := range shipDeckImageUrls {
		t.Run(shipUrlName, func(t *testing.T) {
			crawler := NewCrawler(shipUrlName, true, 30*time.Second)

			result, err := crawler.CrawlShipDeckImages(shipDeckImageUrl)
			if err != nil {
				t.Errorf("Error crawling %s: %v", shipUrlName, err)
				return
			}

			fmt.Printf("Ship %s deck images: %+v\n", shipUrlName, result)
		})
	}
}
