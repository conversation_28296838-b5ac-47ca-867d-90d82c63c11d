package silver_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlerCrawlShipSuites(t *testing.T) {
	shipSuitesUrlMap := map[string]string{
		"dawn": "https://www.silversea.com/ships/silver-dawn.html",
	}

	for shipUrlName, shipSuitesUrl := range shipSuitesUrlMap {
		crawler := NewCrawler(shipUrlName, true, 30*time.Second)

		suites, err := crawler.CrawlShipSuites(shipSuitesUrl)
		if err != nil {
			fmt.Printf("Failed to crawl ship suites: %v", err)
		}

		for i, suite := range suites {
			fmt.Printf("Suite %d: %+v\n", i+1, suite)
		}
	}
}
