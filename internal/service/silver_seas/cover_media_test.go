package silver_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipCoverMedia(t *testing.T) {
	shipCoverMediaUrlMap := map[string]string{
		"dawn": "https://www.silversea.com/ships/silver-dawn.html",
		"ray":  "https://www.silversea.com/ships/silver-ray.html",
	}

	for shipUrlName, shipCoverMediaUrl := range shipCoverMediaUrlMap {
		crawler := NewCrawler(shipUrlName, true, 30*time.Second)

		result, err := crawler.CrawlShipCoverMedia(shipCoverMediaUrl)
		if err != nil {
			fmt.Printf("Failed to crawl cover media for %s: %v", shipUrlName, err)
			continue
		}

		fmt.Printf("Cover Media Result: %+v\n", result)
	}
}
