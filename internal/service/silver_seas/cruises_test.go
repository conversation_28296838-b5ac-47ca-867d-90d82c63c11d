package silver_seas_test

import (
	"fmt"
	"testing"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/service/silver_seas"
)

func TestCrawlShipCruises(t *testing.T) {
	shipUrlMap := map[string]string{
		"dawn": "https://www.silversea.com/ships/silver-dawn.html",
		"ray":  "https://www.silversea.com/ships/silver-ray.html",
	}

	for shipUrlName, url := range shipUrlMap {
		t.Run(shipUrlName, func(t *testing.T) {
			crawler := silver_seas.NewCrawler(shipUrlName, true, 30*time.Second)

			cruises, err := crawler.CrawlShipCruises(url)
			if err != nil {
				t.Errorf("Error crawling %s: %v", shipUrlName, err)
				return
			}

			for _, cruise := range cruises {
				fmt.Printf("cruise: %+v\n", cruise)
			}
		})
	}
}
