package silver_seas

import (
	"fmt"
	"strings"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

func (crawler *SilverSeasCrawler) CrawlShipCoverMedia(shipCoverMediaUrl string) (coverMedia dto.ShipCoverMedia, err error) {
	page, teardown, err := crawler.setupPage(shipCoverMediaUrl)
	if err != nil {
		return coverMedia, err
	}
	defer teardown()

	time.Sleep(2 * time.Second)

	heroBannerContainer, err := page.Element(".HeroBanner-module_background-image-container__zkIlw")
	if err != nil {
		return coverMedia, fmt.Errorf("cannot find hero banner container: %w", err)
	}

	heroImageElement, err := heroBannerContainer.Element("img")
	if err != nil {
		return coverMedia, fmt.Errorf("cannot find hero image: %w", err)
	}

	srcAttr, err := heroImageElement.Attribute("src")
	if err != nil || srcAttr == nil {
		return coverMedia, fmt.Errorf("cannot get image src attribute: %w", err)
	}

	imageBytes, err := tool.FetchFileBytes(*srcAttr)
	if err != nil {
		return coverMedia, fmt.Errorf("failed to download cover image: %w", err)
	}

	s3Path := fmt.Sprintf("ship_%s/silver-seas/%s/cover_media", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)
	fileName := fmt.Sprintf("cover_%s.jpg", crawler.shipUrlName)

	err = service.S3AddFile(imageBytes, fileName, s3Path)
	if err != nil {
		service.Logger.Error().
			Err(err).
			Str("s3FileName", fileName).
			Str("shipUrlName", crawler.shipUrlName).
			Msg("Failed to upload cover image to S3")
		return coverMedia, fmt.Errorf("failed to upload image to S3: %w", err)
	}

	s3FullPath := fmt.Sprintf("%s/%s", s3Path, fileName)
	service.Logger.Info().
		Str("s3Path", s3FullPath).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Successfully uploaded cover image to S3")

	coverMedia.ShipUrlName = crawler.shipUrlName
	coverMedia.S3FilePath = s3FullPath

	return coverMedia, nil
}
