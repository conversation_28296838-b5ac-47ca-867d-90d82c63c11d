package silver_seas

import (
	"fmt"
	"strings"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
	"github.com/go-rod/rod/lib/proto"
)

func (crawler *SilverSeasCrawler) CrawlShipActivity(url string) ([]dto.ShipActivity, error) {
	page, teardown, err := crawler.setupPage(url)
	if err != nil {
		return nil, err
	}
	defer teardown()

	service.ScrollPageDown(page, 3, 1000, 1)

	time.Sleep(2 * time.Second)

	publicAreasContainer, err := page.Element(`[data-content-name="public_areas"]`)
	if err != nil {
		return nil, fmt.Errorf("cannot find public_areas container for URL %s: %v", url, err)
	}

	publicAreasNextButtonSelector := `[data-content-name="public_areas"] button:has(img[alt*="Next"]), [data-content-name="public_areas"] button[aria-label*="Next"]`
	nextButton, err := page.Timeout(5 * time.Second).Element(publicAreasNextButtonSelector)
	if err == nil && nextButton != nil {
		// Found Public Areas next button, clicking through slides to trigger lazy loading
		for {
			isClickable, err := nextButton.Eval(`() => !this.disabled`)
			if err != nil || !isClickable.Value.Bool() {
				service.Logger.Debug().Msg("Next button no longer clickable, all slides viewed")
				break
			}

			err = nextButton.Click(proto.InputMouseButtonLeft, 1)
			if err != nil {
				service.Logger.Debug().Err(err).Msg("Failed to click next button")
				break
			}

			service.Logger.Debug().Msg("Clicked next button, waiting for content to load")

			time.Sleep(500 * time.Millisecond)

			page.Mouse.MustScroll(0, 100)
			time.Sleep(200 * time.Millisecond)
		}

		time.Sleep(1 * time.Second)
	}

	cardElements, err := publicAreasContainer.Elements(".swiper-slide")
	if err != nil {
		return nil, fmt.Errorf("cannot find swiper-slide cards in public_areas for URL %s: %v", url, err)
	}

	if len(cardElements) == 0 {
		return nil, fmt.Errorf("no swiper-slide cards found in public_areas section")
	}

	var activities []dto.ShipActivity
	for _, cardElement := range cardElements {
		activity := dto.ShipActivity{
			ShipUrlName: crawler.shipUrlName,
		}

		titleElement, err := cardElement.Element("h3")
		if err != nil {
			service.Logger.Warn().
				Str("shipUrlName", crawler.shipUrlName).
				Msg("No title found for activity card")
			continue
		}
		activity.Title = strings.TrimSpace(titleElement.MustText())

		descElement, err := cardElement.Element("p")
		if err != nil {
			service.Logger.Warn().
				Str("title", activity.Title).
				Str("shipUrlName", crawler.shipUrlName).
				Msg("No description found for activity")
			continue
		}
		activity.Content = strings.TrimSpace(descElement.MustText())

		imageElement, err := cardElement.Element("img")
		if err == nil && imageElement != nil {
			var imageUrl string
			srcAttr, err := imageElement.Attribute("src")
			if err == nil && srcAttr != nil {
				srcsetAttr, _ := imageElement.Attribute("srcset")
				var srcsetValue string
				if srcsetAttr != nil {
					srcsetValue = *srcsetAttr
				}

				imageUrl = getHighestResolutionImageFromSrcset(srcsetValue, *srcAttr)
			}

			// 因為網站有 lazy loading 機制，所以需要確認 imageUrl 是否為有效的圖片 URL
			if !strings.HasPrefix(imageUrl, "data:") && imageUrl != "" && !strings.Contains(imageUrl, "placeholder") {
				service.Logger.Info().
					Str("imageUrl", imageUrl).
					Str("title", activity.Title).
					Msg("Found activity image, fetching...")

				imageBytes, err := tool.FetchFileBytes(imageUrl)
				if err != nil {
					service.Logger.Error().
						Err(err).
						Str("imageUrl", imageUrl).
						Str("title", activity.Title).
						Msg("Failed to fetch activity image")
				} else {
					s3Path := fmt.Sprintf("ship_%s/silver-seas/%s/activities", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)
					activityName := strings.ToLower(strings.ReplaceAll(activity.Title, " ", "_"))
					s3FileName := fmt.Sprintf("%s.jpg", activityName)

					err = service.S3AddFile(imageBytes, s3FileName, s3Path)
					if err != nil {
						service.Logger.Error().
							Err(err).
							Str("s3FileName", s3FileName).
							Str("title", activity.Title).
							Msg("Failed to upload activity image to S3")
					} else {
						fullS3Path := fmt.Sprintf("%s/%s", s3Path, s3FileName)
						activity.ImageS3Path = fullS3Path

						service.Logger.Info().
							Str("s3Path", fullS3Path).
							Str("title", activity.Title).
							Msg("Successfully uploaded activity image to S3")
					}
				}
			}
		}

		if activity.Title != "" && activity.Content != "" {
			activities = append(activities, activity)

			service.Logger.Info().
				Str("title", activity.Title).
				Str("shipUrlName", crawler.shipUrlName).
				Msg("Found activity")
		}
	}

	service.Logger.Info().
		Int("count", len(activities)).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Successfully crawled ship activities")

	return activities, nil
}
