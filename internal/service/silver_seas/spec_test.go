package silver_seas

import (
	"fmt"
	"testing"
	"time"
)

func TestCrawlShipSpec(t *testing.T) {
	shipUrlMap := map[string]string{
		"dawn": "https://www.silversea.com/ships/silver-dawn.html",
		"ray":  "https://www.silversea.com/ships/silver-ray.html",
	}

	for shipUrlName, shipURL := range shipUrlMap {
		t.Run(shipUrlName, func(t *testing.T) {
			crawler := NewCrawler(shipUrlName, true, 30*time.Second)

			spec, err := crawler.CrawlShipSpec(shipURL)
			if err != nil {
				t.Errorf("Error crawling %s: %v", shipUrlName, err)
				return
			}

			fmt.Printf("Ship %s spec: %+v\n", shipUrlName, spec)
		})
	}
}
