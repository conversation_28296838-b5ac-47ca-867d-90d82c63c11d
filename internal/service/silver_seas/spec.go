package silver_seas

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

func (crawler *SilverSeasCrawler) CrawlShipSpec(specUrl string) (spec dto.ShipSpec, err error) {
	page, teardown, err := crawler.setupPage(specUrl)
	if err != nil {
		return dto.ShipSpec{}, err
	}
	defer teardown()

	time.Sleep(2 * time.Second)

	deckPlanLink, err := page.Element("a[href*='.pdf']")
	if err != nil {
		return dto.ShipSpec{}, err
	}

	pdfUrl, err := deckPlanLink.Attribute("href")
	if err != nil || pdfUrl == nil || *pdfUrl == "" {
		return dto.ShipSpec{}, err
	}

	fileBytes, err := tool.FetchFileBytes(*pdfUrl)
	if err != nil {
		return dto.ShipSpec{}, err
	}

	tempDir := filepath.Join("temp", "deck_images", "silver_seas", crawler.shipUrlName)
	err = os.MkdirAll(tempDir, 0755)
	if err != nil {
		return dto.ShipSpec{}, err
	}

	pdfFileName := fmt.Sprintf("deck_image_%s.pdf", crawler.shipUrlName)
	pdfPath := filepath.Join(tempDir, pdfFileName)

	err = os.WriteFile(pdfPath, fileBytes, 0644)
	if err != nil {
		return dto.ShipSpec{}, err
	}

	imageOutputDir := filepath.Join(tempDir, "images")
	imageFiles, err := tool.ConvertPDFToImages(pdfPath, imageOutputDir)
	if err != nil || len(imageFiles) == 0 {
		return dto.ShipSpec{}, err
	}

	imageBytes, err := os.ReadFile(imageFiles[0])
	if err != nil {
		return dto.ShipSpec{}, err
	}

	if conf.Config.OpenaiApiKey != "" {
		prompt := `分析圖片並萃取出以下資料（數字）並整理成 json 格式:
		- Crew
		- Guests
		- Tonnage (如果格式為 54,700 則為 54700)
		- Length (Metres)
		- Width (Metres)
		- Passenger Decks

		例如：
		{"crew": "200","guests": "400","tonnage": "10000","length": "100","width": "20","passenger_decks": "10"}

		除了 json 本身外不要有任何其他文字，不要有任何其他說明
		`

		openaiResponse, err := tool.UploadImageWithPrompt(imageBytes, prompt, conf.Config.OpenaiApiKey)
		if err != nil {
			service.Logger.Error().Err(err).Msgf("Failed to analyze deck image with OpenAI for ship: %s", crawler.shipUrlName)
		} else {
			// Clean the OpenAI response to remove markdown code block delimiters
			cleanedResponse := tool.CleanOpenAIResponse(openaiResponse)

			type ShipSpecFromDeckImage struct {
				Crew           string `json:"crew"`
				Guests         string `json:"guests"`
				Tonnage        string `json:"tonnage"`
				Length         string `json:"length"`
				Width          string `json:"width"`
				PassengerDecks string `json:"passenger_decks"`
			}

			var shipSpecFromDeckImage ShipSpecFromDeckImage
			err = json.Unmarshal([]byte(cleanedResponse), &shipSpecFromDeckImage)
			if err != nil {
				service.Logger.Error().Err(err).Msgf("Failed to unmarshal cleaned OpenAI response for ship: %s", crawler.shipUrlName)
				return dto.ShipSpec{}, err
			}
			spec.Crew = shipSpecFromDeckImage.Crew
			spec.Guests = shipSpecFromDeckImage.Guests
			spec.Tonnage = shipSpecFromDeckImage.Tonnage
			spec.Length = shipSpecFromDeckImage.Length
			spec.Width = shipSpecFromDeckImage.Width
			spec.Decks = shipSpecFromDeckImage.PassengerDecks
		}
	} else {
		service.Logger.Warn().Msg("OpenAI API key not configured, skipping deck image analysis")
	}

	err = os.RemoveAll(tempDir)
	if err != nil {
		service.Logger.Error().Err(err).Msgf("Failed to clean up temp directory: %s", tempDir)
	}

	spec.ShipUrlName = crawler.shipUrlName

	return spec, nil
}
