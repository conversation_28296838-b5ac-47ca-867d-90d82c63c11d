package silver_seas

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

func (crawler *SilverSeasCrawler) CrawlShipDeckImages(shipDeckImageUrl string) (shipDeckImage dto.ShipDeckImage, err error) {
	page, teardown, err := crawler.setupPage(shipDeckImageUrl)
	if err != nil {
		return dto.ShipDeckImage{}, err
	}
	defer teardown()

	time.Sleep(2 * time.Second)

	deckPlanLink, err := page.Element("a[href*='.pdf']")
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("cannot find deck plan link for URL %s: %v", shipDeckImageUrl, err)
	}

	pdfUrl, err := deckPlanLink.Attribute("href")
	if err != nil || pdfUrl == nil || *pdfUrl == "" {
		return dto.ShipDeckImage{}, fmt.Errorf("cannot get PDF URL from deck plan link: %v", err)
	}

	fileBytes, err := tool.FetchFileBytes(*pdfUrl)
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to fetch PDF file: %v", err)
	}

	tempDir := filepath.Join("temp", "deck_images", "silver_seas", crawler.shipUrlName)
	err = os.MkdirAll(tempDir, 0755)
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to create temp directory: %v", err)
	}

	pdfFileName := fmt.Sprintf("deck_image_%s.pdf", crawler.shipUrlName)
	pdfPath := filepath.Join(tempDir, pdfFileName)

	err = os.WriteFile(pdfPath, fileBytes, 0644)
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to save PDF to temp file: %v", err)
	}

	imageOutputDir := filepath.Join(tempDir, "images")
	imageFiles, err := tool.ConvertPDFToImages(pdfPath, imageOutputDir)
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to convert PDF to images: %v", err)
	}

	service.Logger.Info().Msgf("Successfully converted PDF to %d images in: %s", len(imageFiles), imageOutputDir)
	if len(imageFiles) == 0 {
		return dto.ShipDeckImage{}, fmt.Errorf("no images were generated from PDF")
	}

	s3Path := fmt.Sprintf("ship_%s/silver-seas/%s/deck_image", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)
	var uploadedS3Paths []string

	// Only process the first image
	imagePath := imageFiles[0]
	service.Logger.Info().Msgf("Processing first image: %s", imagePath)

	imageBytes, err := os.ReadFile(imagePath)
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to read image file: %s: %v", imagePath, err)
	}

	s3FileName := fmt.Sprintf("%s.jpg", crawler.shipUrlName)
	err = service.S3AddFile(imageBytes, s3FileName, s3Path)
	if err != nil {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to upload image to S3: %s: %v", s3FileName, err)
	}

	fullS3Path := fmt.Sprintf("%s/%s", s3Path, s3FileName)
	uploadedS3Paths = append(uploadedS3Paths, fullS3Path)
	service.Logger.Info().Msgf("Successfully uploaded image to S3: %s", fullS3Path)

	if len(uploadedS3Paths) == 0 {
		return dto.ShipDeckImage{}, fmt.Errorf("failed to upload any images to S3")
	}

	err = os.RemoveAll(tempDir)
	if err != nil {
		service.Logger.Error().Err(err).Msgf("Failed to clean up temp directory: %s", tempDir)
	}

	service.Logger.Info().Msgf("Successfully uploaded deck image to S3")

	return dto.ShipDeckImage{
		ShipUrlName: crawler.shipUrlName,
		S3FilePath:  uploadedS3Paths[0],
	}, nil
}
