package silver_seas

import (
	"fmt"
	"strings"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
)

func (crawler *SilverSeasCrawler) CrawlShipItineraries(shipItineraryUrl, from, to string) (itineraries []dto.ShipItinerary, mapViewS3Path string, err error) {
	page, teardown, err := crawler.setupPage(shipItineraryUrl)
	if err != nil {
		return nil, "", err
	}
	defer teardown()

	service.ScrollPageDown(page, 5, 1500, 0)

	time.Sleep(2 * time.Second)

	portItems, err := page.Elements("[class*='CruiseItineraryPortItem-module--item']")
	if err != nil {
		return nil, "", fmt.Errorf("failed to find itinerary port items: %w", err)
	}

	service.Logger.Info().
		Int("portItemsCount", len(portItems)).
		Msg("Found Silver Seas itinerary port items")

	for i, portItem := range portItems {
		itinerary := dto.ShipItinerary{}
		fullText, err := portItem.Text()
		if err != nil {
			continue
		}

		// 解析文本內容，預期格式如：
		// 01
		// Belfast , United Kingdom
		// MON, MAY 24
		// 00:00 - 23:00
		lines := strings.Split(strings.TrimSpace(fullText), "\n")
		if len(lines) < 3 {
			continue
		}

		dayNumber := strings.TrimSpace(lines[0])
		portCountryLine := strings.TrimSpace(lines[1])
		if strings.Contains(portCountryLine, ",") {
			parts := strings.Split(portCountryLine, ",")
			if len(parts) >= 2 {
				itinerary.Port = strings.TrimSpace(parts[0])
				itinerary.Country = strings.TrimSpace(parts[len(parts)-1])
			}
		} else {
			itinerary.Port = portCountryLine
		}

		if len(lines) >= 3 {
			dateLine := strings.TrimSpace(lines[2])
			itinerary.Date = dayNumber + " " + dateLine
		}

		if len(lines) >= 4 {
			timeLine := strings.TrimSpace(lines[3])
			if strings.Contains(timeLine, "-") {
				timeParts := strings.Split(timeLine, "-")
				if len(timeParts) == 2 {
					itinerary.ArriveTime = strings.TrimSpace(timeParts[0])
					itinerary.DepartTime = strings.TrimSpace(timeParts[1])
				}
			}
		}

		imageContainer, err := portItem.Element("[class*='CruiseItineraryPortItem-module--port-image-container']")
		if err == nil && imageContainer != nil {
			imgElement, err := imageContainer.Element("img")
			if err == nil && imgElement != nil {
				srcAttr, _ := imgElement.Attribute("src")
				srcsetAttr, _ := imgElement.Attribute("srcset")

				var imageUrl string
				var src, srcset string

				if srcAttr != nil {
					src = *srcAttr
				}
				if srcsetAttr != nil {
					srcset = *srcsetAttr
				}

				imageUrl = getHighestResolutionImageFromSrcset(srcset, src)

				if imageUrl != "" {
					if strings.HasPrefix(imageUrl, "//") {
						imageUrl = "https:" + imageUrl
					} else if strings.HasPrefix(imageUrl, "/") {
						imageUrl = "https://www.silversea.com" + imageUrl
					}

					imageBytes, err := tool.FetchFileBytes(imageUrl)
					if err != nil {
						service.Logger.Error().
							Err(err).
							Str("imageUrl", imageUrl).
							Str("port", itinerary.Port).
							Msg("Failed to fetch Silver Seas itinerary port image")
					} else {
						s3Path := fmt.Sprintf("ship_%s/silver-seas/%s/itineraries", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)

						cleanPort := strings.ToLower(strings.ReplaceAll(itinerary.Port, " ", "_"))
						cleanPort = strings.ReplaceAll(cleanPort, "&", "and")
						cleanPort = strings.ReplaceAll(cleanPort, ",", "")
						s3FileName := fmt.Sprintf("port_%s_%s.jpg", dayNumber, cleanPort)

						err = service.S3AddFile(imageBytes, s3FileName, s3Path)
						if err != nil {
							service.Logger.Error().
								Err(err).
								Str("s3FileName", s3FileName).
								Str("port", itinerary.Port).
								Msg("Failed to upload Silver Seas itinerary port image to S3")
						} else {
							fullS3Path := fmt.Sprintf("%s/%s", s3Path, s3FileName)
							itinerary.ImageS3Path = fullS3Path

							service.Logger.Info().
								Str("s3Path", fullS3Path).
								Str("port", itinerary.Port).
								Msg("Successfully uploaded Silver Seas itinerary port image to S3")
						}
					}
				}
			}
		}

		portItem.MustScrollIntoView()
		time.Sleep(500 * time.Millisecond)

		portItem.MustClick()
		time.Sleep(1 * time.Second)

		expandedContent, err := page.Element("[class*='modal'], [class*='expanded'], [class*='detail']")
		if err == nil && expandedContent != nil {
			descriptionElements, err := expandedContent.Elements("p, div[class*='description'], [class*='content']")
			if err == nil && len(descriptionElements) > 0 {
				for _, descEl := range descriptionElements {
					descText, err := descEl.Text()
					if err == nil && len(descText) > 20 {
						itinerary.DestinationInfo = strings.TrimSpace(descText)
						break
					}
				}
			}

			closeButton, err := expandedContent.Element("button[class*='close'], [class*='close'], .close")
			if err == nil && closeButton != nil {
				closeButton.MustClick()
				time.Sleep(500 * time.Millisecond)
			}
		}

		itineraries = append(itineraries, itinerary)

		service.Logger.Debug().
			Int("itemIndex", i).
			Str("port", itinerary.Port).
			Str("country", itinerary.Country).
			Str("date", itinerary.Date).
			Str("arriveTime", itinerary.ArriveTime).
			Str("departTime", itinerary.DepartTime).
			Msg("Processed Silver Seas itinerary item")
	}

	mapElement, err := page.Element(".CruiseInfoSummary2-module--map-container--5bd95 .SanityImage-module_container__ySxNH img")
	if err == nil && mapElement != nil {
		srcsetAttr, _ := mapElement.Attribute("srcset")

		var srcset string
		if srcsetAttr != nil {
			srcset = *srcsetAttr
		}
		imageUrl := getHighestResolutionImageFromSrcset(srcset, "")
		if imageUrl != "" {
			if strings.HasPrefix(imageUrl, "//") {
				imageUrl = "https:" + imageUrl
			} else if strings.HasPrefix(imageUrl, "/") {
				imageUrl = "https://www.silversea.com" + imageUrl
			}

			imageBytes, err := tool.FetchFileBytes(imageUrl)
			if err != nil {
				service.Logger.Error().
					Err(err).
					Str("imageUrl", imageUrl).
					Msg("Failed to fetch Silver Seas map image")
			} else {
				s3Path := fmt.Sprintf("ship_%s/silver-seas/%s/itineraries", strings.ToLower(conf.Config.ENV), crawler.shipUrlName)
				s3FileName := "map_view.jpg"

				err = service.S3AddFile(imageBytes, s3FileName, s3Path)
				if err != nil {
					service.Logger.Error().
						Err(err).
						Str("s3FileName", s3FileName).
						Msg("Failed to upload Silver Seas map image to S3")
				} else {
					mapViewS3Path = fmt.Sprintf("%s/%s", s3Path, s3FileName)

					service.Logger.Info().
						Str("s3Path", mapViewS3Path).
						Str("imageUrl", imageUrl).
						Msg("Successfully uploaded Silver Seas map image to S3")
				}
			}
		}
	} else {
		service.Logger.Warn().
			Msg("Could not find Silver Seas map image element")
	}

	service.Logger.Info().
		Int("totalItineraries", len(itineraries)).
		Str("mapViewS3Path", mapViewS3Path).
		Msg("Completed Silver Seas itinerary crawling")

	return itineraries, mapViewS3Path, nil
}
