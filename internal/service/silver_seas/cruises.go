package silver_seas

import (
	"fmt"
	"strings"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service"
	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/proto"
)

func (crawler *SilverSeasCrawler) CrawlShipCruises(url string) ([]dto.ShipCruise, error) {
	page, teardown, err := crawler.setupPage(url)
	if err != nil {
		return nil, err
	}
	defer teardown()

	service.ScrollPageDown(page, 5, 1000, 1)
	time.Sleep(1 * time.Second)

	cruiseSection, err := page.Timeout(10 * time.Second).Element(`#cruises`)
	if err == nil && cruiseSection != nil {
		cruiseSection.MustScrollIntoView()
		time.Sleep(1 * time.Second)
	}

	var allCruises []dto.ShipCruise
	currentPage := 1
	for {
		service.Logger.Info().
			Int("page", currentPage).
			Str("shipUrlName", crawler.shipUrlName).
			Msg("Processing cruise page")

		err = page.WaitElementsMoreThan(`ul.SearchResults-module--search-results--7f25e li.SearchResults-module--item--12615`, 0)
		if err != nil {
			service.Logger.Warn().
				Err(err).
				Int("page", currentPage).
				Msg("No cruise cards found on page")
			break
		}

		cruises, err := extractCruises(page, crawler.shipUrlName)
		if err != nil {
			service.Logger.Error().
				Err(err).
				Int("page", currentPage).
				Msg("Failed to extract cruises from page")
			break
		}

		allCruises = append(allCruises, cruises...)

		nextButton, err := findNextPageButton(page)
		if err != nil || nextButton == nil {
			service.Logger.Info().
				Int("totalPages", currentPage).
				Msg("No more pages, finished crawling")
			break
		}

		isDisabled, err := nextButton.Attribute("disabled")
		if err == nil && isDisabled != nil {
			service.Logger.Info().
				Int("totalPages", currentPage).
				Msg("Next button is disabled, finished crawling")
			break
		}

		err = nextButton.Click(proto.InputMouseButtonLeft, 1)
		if err != nil {
			service.Logger.Error().
				Err(err).
				Int("page", currentPage).
				Msg("Failed to click next page button")
			break
		}

		currentPage++
		time.Sleep(1 * time.Second)
	}

	service.Logger.Info().
		Int("totalCruises", len(allCruises)).
		Str("shipUrlName", crawler.shipUrlName).
		Msg("Successfully crawled all ship cruises")

	return allCruises, nil
}

func extractCruises(page *rod.Page, shipUrlName string) ([]dto.ShipCruise, error) {
	cruiseCards, err := page.Elements(`ul.SearchResults-module--search-results--7f25e li.SearchResults-module--item--12615`)
	if err != nil {
		return nil, fmt.Errorf("failed to find cruise cards: %v", err)
	}

	var cruises []dto.ShipCruise

	for i, card := range cruiseCards {
		cruise := dto.ShipCruise{
			ShipUrlName: shipUrlName,
		}

		// Extract title (from-to)
		titleElement, err := card.Element(`h3.EmotionalCruiseCard-module--trip--b7b6a`)
		if err != nil {
			service.Logger.Warn().
				Int("cardIndex", i).
				Msg("Failed to find title element")
			continue
		}
		fullTitle := strings.TrimSpace(titleElement.MustText())
		cruise.Title = fullTitle

		// Parse from and to from title
		titleParts := strings.Split(fullTitle, " to ")
		if len(titleParts) == 2 {
			cruise.From = strings.TrimSpace(titleParts[0])
			cruise.To = strings.TrimSpace(titleParts[1])
		}

		// Extract departure date
		departDateElement, err := card.Element(`div.Dates-module--container--1bf61 time:first-child`)
		if err == nil {
			dateTime, err := departDateElement.Attribute("datetime")
			if err == nil && dateTime != nil {
				// Parse the date to extract month/day and year
				parsedDate, err := time.Parse("2006-01-02", *dateTime)
				if err == nil {
					cruise.DepartDate = parsedDate.Format("Jan 2")
					cruise.DepartYear = parsedDate.Format("2006")
				}
			}
		}

		durationElement, err := card.Element(`div.Dates-module--duration--b740b time`)
		if err == nil {
			durationText := strings.TrimSpace(durationElement.MustText())
			cruise.Duration = durationText
		}

		priceElement, err := card.Element(`div.PriceBox-module_price-value__ZvIGK`)
		if err == nil {
			priceText := strings.TrimSpace(priceElement.MustText())
			// Remove any discount chip text if present
			if strings.Contains(priceText, "$") {
				parts := strings.Split(priceText, " ")
				cruise.Price = parts[len(parts)-1]
			} else {
				cruise.Price = priceText
			}
		}

		promoBadges, err := card.Elements(`div.PromoBadges-module_container__VVZsi div.PromoBadges-module_item__yVQbB`)
		if err == nil && len(promoBadges) > 0 {
			var offers []string
			for _, badge := range promoBadges {
				offerText := strings.TrimSpace(badge.MustText())
				if offerText != "" {
					offers = append(offers, offerText)
				}
			}
			cruise.FeatureOffer = strings.Join(offers, ", ")
		}

		linkElement, err := card.Element(`a.EmotionalCruiseCard-module--container--8e728`)
		if err == nil {
			href, err := linkElement.Attribute("href")
			if err == nil && href != nil {
				cruise.DetailLink = *href
				// Make it absolute URL if needed
				if strings.HasPrefix(cruise.DetailLink, "/") {
					cruise.DetailLink = "https://www.silversea.com" + cruise.DetailLink
				}
			}
		}

		imageElement, err := card.Element(`div.EmotionalCruiseCard-module--image-container--04c5b img`)
		if err == nil {
			var imageUrl string
			srcset, err := imageElement.Attribute("srcset")
			if err == nil && srcset != nil && *srcset != "" {
				imageUrl = getHighestResolutionImageFromSrcset(*srcset, "")
			}

			if err == nil && imageUrl != "" {
				service.Logger.Info().
					Str("imageUrl", imageUrl).
					Str("title", cruise.Title).
					Msg("Found cruise image, fetching...")

				imageBytes, err := tool.FetchFileBytes(imageUrl)
				if err != nil {
					service.Logger.Error().
						Err(err).
						Str("imageUrl", imageUrl).
						Str("title", cruise.Title).
						Msg("Failed to fetch cruise image")
				} else {
					s3Path := fmt.Sprintf("ship_%s/silver-seas/%s/cruises", strings.ToLower(conf.Config.ENV), shipUrlName)
					fileName := fmt.Sprintf("%s_%s_%s.jpg",
						strings.ToLower(strings.ReplaceAll(cruise.From, " ", "_")),
						strings.ToLower(strings.ReplaceAll(cruise.To, " ", "_")),
						strings.ReplaceAll(cruise.DepartDate, " ", "_"))

					err = service.S3AddFile(imageBytes, fileName, s3Path)
					if err != nil {
						service.Logger.Error().
							Err(err).
							Str("fileName", fileName).
							Str("title", cruise.Title).
							Msg("Failed to upload cruise image to S3")
					} else {
						fullS3Path := fmt.Sprintf("%s/%s", s3Path, fileName)
						cruise.ImageS3Path = fullS3Path

						service.Logger.Info().
							Str("s3Path", fullS3Path).
							Str("title", cruise.Title).
							Msg("Successfully uploaded cruise image to S3")
					}
				}
			}
		}

		if cruise.Title != "" && cruise.DetailLink != "" {
			cruises = append(cruises, cruise)
			service.Logger.Info().
				Str("title", cruise.Title).
				Str("from", cruise.From).
				Str("to", cruise.To).
				Str("departDate", cruise.DepartDate).
				Str("duration", cruise.Duration).
				Str("price", cruise.Price).
				Msg("Found cruise")
		}
	}

	return cruises, nil
}

func findNextPageButton(page *rod.Page) (*rod.Element, error) {
	selectors := []string{
		`div.Pagination-module_container__JHnDH button[data-content-piece="next"]:not([disabled])`,
		`button[aria-label="Next page"]:not([disabled])`,
		`button:has(svg use[href*="chevron-right"]):not([disabled])`,
	}

	for _, selector := range selectors {
		nextButton, err := page.Timeout(5 * time.Second).Element(selector)
		if err == nil && nextButton != nil {
			// Check if button is visible and enabled
			visible, err := nextButton.Visible()
			if err == nil && visible {
				return nextButton, nil
			}
		}
	}

	return nil, fmt.Errorf("next page button not found")
}
