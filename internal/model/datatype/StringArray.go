package datatype

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

type StringArray []string

// Value return json value, implement driver.Valuer interface
func (m StringArray) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	ba, err := m.MarshalJSON()
	return string(ba), err
}

// Scan scan value into Jsonb, implements sql.Scanner interface
func (m *StringArray) Scan(val interface{}) error {
	if val == nil {
		*m = StringArray{}
		return nil
	}
	var ba []byte
	switch v := val.(type) {
	case []byte:
		ba = v
	case string:
		ba = []byte(v)
	default:
		return errors.New(fmt.Sprint("Failed to unmarshal JSONB value:", val))
	}

	return m.UnmarshalJSON(ba)
}

func (m StringArray) MarshalJSON() ([]byte, error) {
	if m == nil {
		return []byte("null"), nil
	}

	t := ([]string)(m)
	return json.Marshal(t)
}

func (m *StringArray) UnmarshalJSON(b []byte) error {
	t := []string{}
	err := json.Unmarshal(b, &t)
	*m = StringArray(t)
	return err
}

// GormDataType gorm common data type
func (m StringArray) GormDataType() string {
	return "jsonarr"
}

// GormDBDataType gorm db data type
func (StringArray) GormDBDataType(db *gorm.DB, field *schema.Field) string {
	switch db.Dialector.Name() {
	case "sqlite":
		return "JSON"
	case "mysql":
		return "JSON"
	case "postgres":
		return "JSONB"
	}
	return ""
}
