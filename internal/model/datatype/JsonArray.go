package datatype

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

type JsonArray []string

// Value return json value, implement driver.Valuer interface
func (m JsonArray) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	ba, err := m.MarshalJSON()
	return string(ba), err
}

// Scan scan value into Jsonb, implements sql.Scanner interface
func (m *JsonArray) Scan(val interface{}) error {
	if val == nil {
		*m = JsonArray{}
		return nil
	}
	var ba []byte
	switch v := val.(type) {
	case []byte:
		ba = v
	case string:
		ba = []byte(v)
	default:
		return errors.New(fmt.Sprint("Failed to unmarshal JSONB value:", val))
	}

	return m.UnmarshalJSON(ba)
}

func (m JsonArray) MarshalJSON() ([]byte, error) {
	if m == nil {
		return []byte("null"), nil
	}

	t := ([]string)(m)
	return json.Marshal(t)
}

func (m *JsonArray) UnmarshalJSON(b []byte) error {
	t := []string{}
	err := json.Unmarshal(b, &t)
	*m = JsonArray(t)
	return err
}

// GormDataType gorm common data type
func (m JsonArray) GormDataType() string {
	return "jsonarr"
}

// GormDBDataType gorm db data type
func (JsonArray) GormDBDataType(db *gorm.DB, field *schema.Field) string {
	switch db.Dialector.Name() {
	case "sqlite":
		return "JSON"
	case "mysql":
		return "JSON"
	case "postgres":
		return "JSONB"
	}
	return ""
}
