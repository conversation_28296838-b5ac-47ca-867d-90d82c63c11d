package datatype

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

type IntArray []int

// Value return json value, implement driver.Valuer interface
func (m IntArray) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	ba, err := m.MarshalJSON()
	return string(ba), err
}

// Scan scan value into Jsonb, implements sql.Scanner interface
func (m *IntArray) Scan(val interface{}) error {
	if val == nil {
		*m = IntArray{}
		return nil
	}
	var ba []byte
	switch v := val.(type) {
	case []byte:
		ba = v
	case string:
		ba = []byte(v)
	default:
		return errors.New(fmt.Sprint("Failed to unmarshal JSONB value:", val))
	}

	return m.UnmarshalJSON(ba)
}

func (m IntArray) MarshalJSON() ([]byte, error) {
	if m == nil {
		return []byte("null"), nil
	}

	t := ([]int)(m)
	return json.Marshal(t)
}

func (m *IntArray) UnmarshalJSON(b []byte) error {
	var t []int
	err := json.Unmarshal(b, &t)
	*m = IntArray(t)
	return err
}

// GormDataType gorm common data type
func (m IntArray) GormDataType() string {
	return "jsonarr"
}

// GormDBDataType gorm db data type
func (IntArray) GormDBDataType(db *gorm.DB, field *schema.Field) string {
	switch db.Dialector.Name() {
	case "sqlite":
		return "JSON"
	case "mysql":
		return "JSON"
	case "postgres":
		return "JSONB"
	}
	return ""
}
