package model

import (
	"time"
)

type CrawlerShipTask struct {
	GormModel
	Type         string     `gorm:"type:varchar(50);not null;comment:任務類型 (schedule, manual)"`
	Method       string     `gorm:"type:varchar(50);not null;comment:爬取方法"`
	Body         string     `gorm:"type:text;comment:爬取參數"`
	ResponseJson string     `gorm:"type:json;comment:爬取後 JSON 資料"`
	StartTime    *time.Time `gorm:"type:datetime;comment:開始時間"`
	EndTime      *time.Time `gorm:"type:datetime;comment:結束時間"`
	Duration     string     `gorm:"type:varchar(50);comment:執行時長(如: 1h2m3s)"`
	Status       string     `gorm:"type:varchar(20);default:'pending';comment:任務狀態"`
	ErrorMsg     string     `gorm:"type:text;comment:錯誤訊息"`
}
