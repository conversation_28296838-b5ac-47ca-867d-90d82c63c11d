package model

import (
	"context"
	"fmt"
	"math"
	"os"
	"strings"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/env"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto/common"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"github.com/rs/zerolog/log"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/logger"
)

var config = conf.Config

type DbContext struct {
	dsn string
	DB  *gorm.DB
}

// Ctx Default DB Context
var Ctx = &DbContext{}

func (ctx *DbContext) Connect() {
	if ctx.dsn == "" {
		log.Panic().Msg("DB dsn is empty")
		return
	}

	logLevel := logger.Silent
	if config.SqlDebug {
		logLevel = logger.Info
	}

	db, err := gorm.Open(mysql.Open(ctx.dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	})
	if err != nil {
		log.Panic().Msg("failed to connect database: " + err.Error())
		return
	}

	sqlDB, err := db.DB()
	if err != nil {
		panic("failed to connect database")
	}

	// SetMaxIdleConns sets the maximum number of connections in the idle connection pool.
	sqlDB.SetMaxIdleConns(20)

	// SetMaxOpenConns sets the maximum number of open connections to the database.
	sqlDB.SetMaxOpenConns(100)

	// SetConnMaxLifetime sets the maximum amount of connections in the idle connection pool.
	sqlDB.SetConnMaxIdleTime(time.Second * 5)

	// SetConnMaxLifetime sets the maximum amount of time a connection may be reused.
	sqlDB.SetConnMaxLifetime(time.Second * 10)

	ctx.DB = db

	if config.SqlAutoMigrate {
		ctx.Migrate()
	}
}

func (ctx *DbContext) InitWithDSN(dsn string) {
	ctx.dsn = dsn
	ctx.Connect()
}

func SaveOrUpdate(data interface{}) error {
	result := Ctx.DB.Clauses(clause.OnConflict{
		UpdateAll: true,
	}).Create(data)
	return result.Error
}

type RecorderLogger struct {
	logger.Interface
	Statements []string
}

// Trace 自定義 logger 給 migrate session 使用
func (r *RecorderLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	sql, _ := fc()
	keywords := []string{
		"select database()",
		"information_schema",
		"select * from `",
	}
	skip := false
	for _, keyword := range keywords {
		if strings.Contains(strings.ToLower(sql), keyword) {
			skip = true
			break
		}
	}
	if !skip {
		fmt.Println(sql, ";")
	}
	r.Statements = append(r.Statements, sql)
}

func Paginate(r *common.Paging) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {

		switch {
		case r.NumberPerPage > 100:
			r.NumberPerPage = 100
			fmt.Errorf("override NumberPerPage from %d to 100, try raw sql limit paging.GetOffset() + paging.NumberPerPage in this case", r.NumberPerPage)
		case r.NumberPerPage <= 0:
			r.NumberPerPage = 0
		}

		return db.Offset(r.GetOffset()).Limit(r.NumberPerPage)
	}
}

func UpdatePaging(db *gorm.DB, r *common.Paging) *gorm.DB {
	db.Offset(-1).Limit(-1).Count(&r.TotalSize)
	r.TotalPageSize = int64(math.Ceil(float64(r.TotalSize) / float64(r.NumberPerPage)))
	if r.PageNumber >= r.TotalPageSize {
		r.IsLastPage = true
	}

	return nil
}

func (ctx *DbContext) Migrate() {

	// Create the database. This is a one-time step.
	// Comment out if running multiple times - You may see an error otherwise
	//db.Exec("CREATE DATABASE gorm-example")
	//db.Exec("USE gorm-example")

	if !tool.InArray(strings.ToUpper(config.ENV), []string{
		env.LOCAL,
		env.DEV,
	}) {
		fmt.Errorf("allow Auto-Migrate only at local & dev, please tune off SQL_AUTO_MIGRATE")
		return
	}

	recorder := tool.GormRecorder{
		Interface:  logger.Default.LogMode(logger.Info),
		Statements: []string{},
	}
	session := ctx.DB.Session(&gorm.Session{
		Logger: &recorder,
	})

	// 暫時解除 FK 約束，避免新增失敗
	session.DisableForeignKeyConstraintWhenMigrating = true

	// =================================================================================================================

	if err := session.AutoMigrate(CrawlerShipTask{}); err != nil {
		log.Error().Msg("AutoMigrate failed: " + err.Error())
	}

	// =================================================================================================================

	// 注意：只要開啟要使用的就好，否則會花很多時間收拾

	session.DisableForeignKeyConstraintWhenMigrating = false

	fmt.Println("\nExit Gorm Migrate, set env SQL_AUTO_MIGRATE = false")
	os.Exit(0)
}
