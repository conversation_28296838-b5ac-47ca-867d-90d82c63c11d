package claim

const (
	IsGuest          = "is_guest"
	IsLogin          = "is_login"
	Authorization    = "auth"
	Token            = "token"
	Username         = "username"
	UserId           = "user_id"
	Roles            = "roles"
	Permissions      = "permissions"
	Currency         = "currency"
	Lang             = "lang"
	IsDefaultLang    = "is_def_lang"
	TraceId          = "trace_id"
	Org              = "org"
	App              = "app"
	PlatClientId     = "client_id"
	PlatClientSecret = "client_secret"
	AppName2Words    = "app_name_2_words"
	FromApi          = "from_api"         // 從 API Secret 來
	IsApiTestMode    = "is_api_test_mode" // 從 API Secret 來，且是測試模式
)
