package language

import "strings"

const (
	ZhTw      = "zh-TW"
	ZhCn      = "zh-CN"
	En        = "en"
	Jp        = "jp"
	ZhTwLower = "zh-tw"
	ZhCnLower = "zh-cn"
	ZhTwShort = "tw"
	ZhCnShort = "cn"
)

func LangTableColumn(lang string) string {
	column := "_"
	switch strings.ToLower(lang) {
	case ZhTwLower:
		column += ZhTwShort
	case ZhCnLower:
		column += ZhCnShort
	case En:
		column = ""
	case "":
		column = ""
	default:
		column += lang
	}

	return column
}
