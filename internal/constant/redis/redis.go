package redis

const (
	MetaPhoneCode   = "meta_phone_code"
	MetaPhoneCodeDB = 3

	MetaTitle   = "meta_title"
	MetaTitleDB = 3

	MetaCountry   = "meta_country"
	MetaCountryDB = 3

	MetaCity   = "meta_city"
	MetaCityDB = 3

	TPLoyaltyCode = "group_loyalty"
	TPLoyaltyDB   = 3

	TokenIntrospect   = "token_introspect"
	TokenIntrospectDB = 3

	Platform   = "platform"
	PlatformDB = 3

	OrderCategoryLang   = "order_category_lang"
	OrderCategoryLangDB = 3

	OrderStatusLang   = "order_status_lang"
	OrderStatusLangDB = 3

	BeUser   = "user"
	BeUserDB = 3

	SecretIntrospect   = "secret_introspect"
	SecretIntrospectDB = 3

	FunctionLock   = "function_lock"
	FunctionLockDB = 3

	RateCode   = "rate_code"
	RateCodeDB = 3

	TravelportCounter   = "travelportCounter"
	TravelportCounterDB = 1

	SearchSuggest   = "search_suggest"
	SearchSuggestDB = 5

	AccessCounter   = "access_counter"
	AccessCounterDB = 5

	Currency   = "currency"
	CurrencyDB = 3

	AsyncStatus   = "async_status"
	AsyncStatusDB = 3

	PlatformRole   = "platform_role"
	PlatformRoleDB = 3

	MembershipBenefits  = "membership_benefits"
	MembershipBenefitDB = 3
)
