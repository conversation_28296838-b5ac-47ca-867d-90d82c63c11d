package status

const (
	Authorized    = "au"
	Validation    = "vd"
	NotFound      = "nf"
	NotChange     = "nc"
	Exist         = "et"
	Database      = "db"
	Redis         = "rd"
	BusinessLogic = "bu"
	Permission    = "pe"
	Parse         = "ps"
	CallApi       = "ca"
	Config        = "cf"
	Notify        = "nt"
	Timeout       = "to"
	Export        = "ep"
	Unknown       = "uk"

	AuthorizedUnLogin             = Authorized + "0001"
	AuthorizedUserOrPassWrong     = Authorized + "0002"
	AuthorizedOrgAppRequired      = Authorized + "0003"
	AuthorizedAppNotActivated     = Authorized + "0004"
	AuthorizedAppNotExist         = Authorized + "0005"
	AuthorizedMainServiceNotReady = Authorized + "0006"
	AuthorizedServiceMaintainMode = Authorized + "0007"
	AuthorizedOrgNotExist         = Authorized + "0008"

	AuthorizedAccountNotExist     = Authorized + "0102"
	AuthorizedAccountNotInService = Authorized + "0103"
	// AuthorizedTokenInvalid 會導致前端直接登出
	AuthorizedTokenInvalid              = Authorized + "0104"
	AuthorizedAccountIsUsed             = Authorized + "0105"
	AuthorizedAccountNoRule             = Authorized + "0106"
	AuthorizedAccountBlackList          = Authorized + "0107"
	AuthorizedAccountReqToManyTime      = Authorized + "0108"
	AuthorizedRefreshTokenInvalid       = Authorized + "0109"
	AuthorizedRefreshTokenExpired       = Authorized + "0110"
	AuthorizedWrongPasswordTooManyTimes = Authorized + "0111"

	AuthorizedCheckVerifyCodeExpired    = Authorized + "0206"
	AuthorizedCheckVerifyCodeNoSend     = Authorized + "0207"
	AuthorizedCheckVerifyCodeWrong      = Authorized + "0208"
	AuthorizedCheckVerifyCodeTooFast    = Authorized + "0209"
	AuthorizedCheckVerifyCodeHasNotSent = Authorized + "0210"
	AuthorizedCheckVerifyInMin          = Authorized + "0211"

	PermissionNotAllowService = Permission + "0001"

	ParseImageConversionFailed = Parse + "0001"

	// ValidationRequired https://github.com/go-playground/validator

	ValidationEqField               = Validation + "0001" // Validations - Fields 1~
	ValidationIp                    = Validation + "0101" // Validations - Network 101~
	ValidationAlpha                 = Validation + "0201" // Validations - String 201~
	ValidationAlphaNum              = Validation + "0202"
	ValidationBoolean               = Validation + "0203"
	ValidationContains              = Validation + "0204"
	ValidationContainsAny           = Validation + "0205"
	ValidationExcludes              = Validation + "0206"
	ValidationStartsNotWith         = Validation + "0207"
	ValidationStartsWith            = Validation + "0208"
	ValidationEndsNotWith           = Validation + "0209"
	ValidationEndsWith              = Validation + "0210"
	ValidationLowercase             = Validation + "0211"
	ValidationUppercase             = Validation + "0212"
	ValidationNumber                = Validation + "0213"
	ValidationNumeric               = Validation + "0214"
	ValidationBase64                = Validation + "0301" // Validations - Format 301~
	ValidationDatetime              = Validation + "0302"
	ValidationEmail                 = Validation + "0303"
	ValidationJson                  = Validation + "0304"
	ValidationJwt                   = Validation + "0305"
	ValidationLatitude              = Validation + "0306"
	ValidationLongitude             = Validation + "0307"
	ValidationUuid                  = Validation + "0308"
	ValidationMd5                   = Validation + "0309"
	ValidationEq                    = Validation + "0401" // Validations - Comparisons 401~
	ValidationNe                    = Validation + "0402"
	ValidationGt                    = Validation + "0403"
	ValidationGte                   = Validation + "0404"
	ValidationLt                    = Validation + "0405"
	ValidationLte                   = Validation + "0406"
	ValidationLen                   = Validation + "0501" // Validations - Others 501~
	ValidationMax                   = Validation + "0502"
	ValidationMin                   = Validation + "0503"
	ValidationRequired              = Validation + "0504"
	ValidationRequiredIf            = Validation + "0505"
	ValidationOneOf                 = Validation + "0507"
	ValidationTypeInvalid           = Validation + "0603" // System
	ValidationReCaptcha             = Validation + "0604"
	ValidationPasswordNotAllowBlank = Validation + "0701" // Validations - Platform
	ValidationPasswordLen           = Validation + "0702"

	ValidationOrderId            = Validation + "1001" // Customize 1000~
	ValidationYmd                = Validation + "1002"
	ValidationPassword           = Validation + "1003"
	ValidationFormat             = Validation + "1004"
	ValidationBodyRequired       = Validation + "1005"
	ValidationFileExtension      = Validation + "1006"
	ValidationPagingSortNotExist = Validation + "1007"

	ValidationRequestDuplicated = Validation + "2001"
	ValidationUnexpected        = Validation + "9998"
	ValidationUndefined         = Validation + "9999"

	DatabaseSelect          = Database + "0001" // 通常隱含 Not Found 處理
	DatabaseInsert          = Database + "0002"
	DatabaseInsertOrUpdate  = Database + "0003"
	DatabaseUpdate          = Database + "0004"
	DatabaseDelete          = Database + "0005"
	DatabaseSave            = Database + "0006"
	DatabaseSaveOrUpdate    = Database + "0007"
	DatabaseTransaction     = Database + "0008"
	DatabaseCreateInBatches = Database + "0009"
	DatabaseGenerateId      = Database + "0010"
	DatabaseConnect         = Database + "0011"

	ConfigUndefined = Config + "0001"

	// 匯出
	ExportIsExporting  = Export + "0001"
	ExportFailed       = Export + "0002"
	ExportNeedBirthday = Export + "0003"

	BusinessLogicCreditPrefix          = BusinessLogic + "0001"
	BusinessLogicLoyaltyFormat         = BusinessLogic + "0002"
	BusinessLogicBooking               = BusinessLogic + "0003"
	BusinessLogicOrderStatus           = BusinessLogic + "0004"
	BusinessLogicProfileLosingPassport = BusinessLogic + "0005"
	BusinessLogicEmailDomain           = BusinessLogic + "0006"
	BusinessLogicFunctionNotAllow      = BusinessLogic + "0007"
	BusinessLogicExist                 = BusinessLogic + "0008"
	BusinessLogicValidationError       = BusinessLogic + "0009"
	BusinessLogicNotAllowOrgUser       = BusinessLogic + "0010"

	BusinessLogicHotelBookingTestMode               = BusinessLogic + "1001"
	BusinessLogicHotelBookingDebugMode              = BusinessLogic + "1002"
	BusinessLogicHotelBookingCreateRecordFailed     = BusinessLogic + "1003"
	BusinessLogicHotelBookingCreditCardTypeUnknown  = BusinessLogic + "1004"
	BusinessLogicHotelBookingNotFoundHotel          = BusinessLogic + "1005"
	BusinessLogicHotelBookingSSRHaveInvalidKey      = BusinessLogic + "1006"
	BusinessLogicHotelBookingSSRSGGroupMaxLength    = BusinessLogic + "1007"
	BusinessLogicHotelBookingSSRMaxLength           = BusinessLogic + "1008"
	BusinessLogicHotelBookingFailed                 = BusinessLogic + "1009"
	BusinessLogicHotelBookingUnknown                = BusinessLogic + "1010"
	BusinessLogicHotelBookingNotSupportChild        = BusinessLogic + "1011"
	BusinessLogicHotelBookingOverQtyMonth           = BusinessLogic + "1012"
	BusinessLogicHotelBookingNotSupportOver1Billion = BusinessLogic + "1013"
	BusinessLogicHotelBookingNoQuota                = BusinessLogic + "1014"
	BusinessLogicHotelBookingSSRChildrenMaxLength   = BusinessLogic + "1015"
	BusinessLogicHotelBookingCancelNotAllow         = BusinessLogic + "1016"
	BusinessLogicHotelBookingPriceMaintain          = BusinessLogic + "1017"
	BusinessLogicHotelBookingDailyPriceNotComplete  = BusinessLogic + "1018"
	BusinessLogicHotelBookingPrice                  = BusinessLogic + "1019"
	BusinessLogicHotelBookingFormRequired           = BusinessLogic + "1020"
	// 酒店預訂 - 對應 Travelport 的錯誤代碼
	BusinessLogicHotelBookingTpError = BusinessLogic + "1201"

	// 產品相關
	BusinessLogicProductIsNotPublished = BusinessLogic + "2001"

	// 訂單相關
	BusinessLogicOrderIsNotShow                         = BusinessLogic + "2201"
	BusinessLogicOrderAlreadyPaid                       = BusinessLogic + "2202"
	BusinessLogicOrderAmountTooLow                      = BusinessLogic + "2203"
	BusinessLogicOrderExchangeRateIsZero                = BusinessLogic + "2204"
	BusinessLogicOrderReachPickupLimitOfPeople          = BusinessLogic + "2205"
	BusinessLogicOrderPickupTimeIsTooLate               = BusinessLogic + "2206"
	BusinessLogicOrderFastTrackTimeIsUnavailable        = BusinessLogic + "2207"
	BusinessLogicOrderFastTrackTimeIsTooClose           = BusinessLogic + "2208"
	BusinessLogicOrderUserNotMatch                      = BusinessLogic + "2209"
	BusinessLogicOrderOperatorNotMatch                  = BusinessLogic + "2210"
	BusinessLogicOrderOperatorIsEmpty                   = BusinessLogic + "2211"
	BusinessLogicOrderOperatorAlreadyExist              = BusinessLogic + "2212"
	BusinessLogicOrderStatusCouldNotBeCancelled         = BusinessLogic + "2213"
	BusinessLogicOrderStatusNotMatch                    = BusinessLogic + "2214"
	BusinessLogicOrderStartDateMustBeEarlierThanEndDate = BusinessLogic + "2215"
	BusinessLogicOrderUserIdCouldNotBeEmpty             = BusinessLogic + "2216"
	BusinessLogicOrderAmountMustBeGreaterThanZero       = BusinessLogic + "2217"
	BusinessLogicOrderTailorMadeNotFound                = BusinessLogic + "2218"
	BusinessLogicOrderTailorMadeStatusCanNotBeCancelled = BusinessLogic + "2219"

	// 支付相關
	BusinessLogicOrderTradePreviousNotPaid                    = BusinessLogic + "2401"
	BusinessLogicOrderTradePaymentCategoryNotMatch            = BusinessLogic + "2402"
	BusinessLogicOrderTradePaymentUnknownStatus               = BusinessLogic + "2403"
	BusinessLogicOrderTradeExternalIdentityCodeNotMatch       = BusinessLogic + "2404"
	BusinessLogicOrderTradePaymentAmountMustBeGreaterThanZero = BusinessLogic + "2405"
	BusinessLogicOrderTradePaymentAmountNotMatch              = BusinessLogic + "2406"
	BusinessLogicOrderTradePaymentNotAllowToModify            = BusinessLogic + "2407"
	BusinessLogicOrderTradePaymentRefundFail                  = BusinessLogic + "2408"

	// 私人訂製
	BusinessLogicTailorMadeStartDateMustBeEarlierThanEndDate = BusinessLogic + "2601"
	BusinessLogicTailorMadeMembershipNeedToHighThanElite     = BusinessLogic + "2602"
	BusinessLogicTailorMadeUnknownOrderType                  = BusinessLogic + "2603"
	BusinessLogicTailorMadeMembershipNeedToHighThanPlatinum  = BusinessLogic + "2604"

	// Blog
	BusinessLogicHotBlogNumberReachLimit         = BusinessLogic + "2801"
	BusinessLogicBlogCategoryHasBlogCanNotDelete = BusinessLogic + "2802"
	BusinessLogicBlogTagHasBlogCanNotDelete      = BusinessLogic + "2803"
	BusinessLogicBlogUrlExist                    = BusinessLogic + "2804"
	BusinessLogicBlogNameExist                   = BusinessLogic + "2805"

	// 分銷相關
	BusinessLogicAgentReportRemittedCannotBeDeleted                  = BusinessLogic + "3001"
	BusinessLogicAgentReportRemittedCannotBeUpdated                  = BusinessLogic + "3002"
	BusinessLogicHotelOrderCommissionIncomeNotFound                  = BusinessLogic + "3003"
	BusinessLogicOrderCommissionIncomeIsShareRemitted                = BusinessLogic + "3004"
	BusinessLogicOrderCommissionIncomeOrderMustBeOrdered             = BusinessLogic + "3005"
	BusinessLogicAgentCommissionReportHotelOrderMappingAlreadyExists = BusinessLogic + "3006"
	BusinessLogicAgentCommissionReportHotelOrderNotBelongToAgent     = BusinessLogic + "3007"
	BusinessLogicAgentCommissionReportAlreadyRemitted                = BusinessLogic + "3008"
	BusinessLogicAgentCommissionHotelOrderAlreadyInReport            = BusinessLogic + "3009"
)

// MessageEn 英文錯誤訊息
var MessageEn = map[string]string{
	Authorized:    "Authorized Error",
	Validation:    "Validation Error",
	NotFound:      "Not Found",
	NotChange:     "Not Change",
	Database:      "Database Error",
	Redis:         "Redis Error",
	BusinessLogic: "BusinessLogic Error",
	Permission:    "Permission Error",
	Parse:         "Parse Error",
	CallApi:       "External service Error",
	Notify:        "Notify Error",
	Export:        "Export Error",
	Unknown:       "Unknown Error",

	AuthorizedUnLogin:             "Login first",
	AuthorizedUserOrPassWrong:     "Account or password is incorrect",
	AuthorizedOrgAppRequired:      "Missing required parameter Org/App",
	AuthorizedAppNotActivated:     "The organization's App is not activated",
	AuthorizedAppNotExist:         "This organization App does not exist",
	AuthorizedMainServiceNotReady: "The main service is not enabled or cannot communicate",
	AuthorizedServiceMaintainMode: "System under maintenance",
	AuthorizedOrgNotExist:         "Organization Not exist",

	AuthorizedAccountNotExist:           "Account does not exist",
	AuthorizedAccountNotInService:       "The account is not in this service",
	AuthorizedTokenInvalid:              "Token is invalid",
	AuthorizedAccountIsUsed:             "This account has been used",
	AuthorizedAccountNoRule:             "This account is not bound to a role",
	AuthorizedAccountBlackList:          "Account is temporarily locked",
	AuthorizedAccountReqToManyTime:      "Verification too frequent",
	AuthorizedRefreshTokenInvalid:       "Refresh token is invalid",
	AuthorizedRefreshTokenExpired:       "Refresh token is expired",
	AuthorizedWrongPasswordTooManyTimes: "Password verification failed too many times, please try again later.",

	AuthorizedCheckVerifyCodeExpired:    "Verification code has expired",
	AuthorizedCheckVerifyCodeNoSend:     "Verification code not sent",
	AuthorizedCheckVerifyCodeWrong:      "Verification code error",
	AuthorizedCheckVerifyCodeTooFast:    "Resend time too short",
	AuthorizedCheckVerifyCodeHasNotSent: "The verification code has not been sent yet",
	AuthorizedCheckVerifyInMin:          "The verification time has exceeded and the verification code has expired. Please obtain the verification letter again",

	PermissionNotAllowService: "This account does not have this permission",

	ParseImageConversionFailed: "Image conversion failed",

	ValidationEqField:               "%s must be the same as %s",
	ValidationIp:                    "%s must be IP format",
	ValidationAlpha:                 "%s must be English",
	ValidationAlphaNum:              "%s must be words and numbers",
	ValidationBoolean:               "%s must be Boolean",
	ValidationContains:              "%s must contain keyword %s",
	ValidationContainsAny:           "%s must contain any of the required keywords",
	ValidationExcludes:              "%s must exclude keyword %s",
	ValidationStartsNotWith:         "%s must not start with %s",
	ValidationStartsWith:            "%s must start with %s",
	ValidationEndsNotWith:           "%s must not end with %s",
	ValidationEndsWith:              "%s must end with %s",
	ValidationLowercase:             "%s must be all lowercase",
	ValidationUppercase:             "%s must be all uppercase",
	ValidationNumber:                "%s must be a number",
	ValidationNumeric:               "%s must be numeric",
	ValidationBase64:                "%s must be base64 encoded",
	ValidationDatetime:              "%s date format error(%s)",
	ValidationEmail:                 "%s must be email format",
	ValidationJson:                  "%s must be valid JSON format",
	ValidationJwt:                   "%s must be valid JWT format",
	ValidationLatitude:              "%s must be valid latitude coordinate",
	ValidationLongitude:             "%s must be valid longitude coordinate",
	ValidationUuid:                  "%s must be valid UUID format",
	ValidationMd5:                   "%s must be valid MD5 hash",
	ValidationEq:                    "%s should be equal to %s",
	ValidationNe:                    "%s should not be equal to %s",
	ValidationGt:                    "%s is too small",
	ValidationGte:                   "%s is too small",
	ValidationLt:                    "%s is too large",
	ValidationLte:                   "%s is too large",
	ValidationLen:                   "%s length must be equal to %s",
	ValidationMax:                   "%s max length must not exceed %s",
	ValidationMin:                   "%s min length must not be less than %s",
	ValidationRequired:              "%s is required",
	ValidationOneOf:                 "%s must be one of defined values",
	ValidationRequiredIf:            "%s is required when %s is present",
	ValidationReCaptcha:             "reCaptcha validation error",
	ValidationTypeInvalid:           "Field type is illegal",
	ValidationPasswordNotAllowBlank: "No spaces allowed in password",
	ValidationPasswordLen:           "Password length is too short",

	ValidationRequestDuplicated: "Request duplicated",
	ValidationUndefined:         "%s Wrong format",

	ValidationOrderId:            "%s order number format invalid",
	ValidationYmd:                "%s date format invalid(2000-01-01)",
	ValidationPassword:           "%s password format invalid",
	ValidationFormat:             "%s data type must be %s",
	ValidationBodyRequired:       "The requested Body data must not be empty",
	ValidationFileExtension:      "Illegal file extension",
	ValidationPagingSortNotExist: "The specified sorting field does not exist",

	DatabaseSelect:          "Database select error",
	DatabaseInsert:          "Database insert error",
	DatabaseInsertOrUpdate:  "Database insertUpdate error",
	DatabaseUpdate:          "Database update error",
	DatabaseDelete:          "Database delete error",
	DatabaseSave:            "Database save error",
	DatabaseSaveOrUpdate:    "Database save or update error",
	DatabaseTransaction:     "Database transaction error",
	DatabaseCreateInBatches: "Database CreateInBatches error",
	DatabaseGenerateId:      "Database Generate Id failed",
	DatabaseConnect:         "Database Connect failed",

	ConfigUndefined: "Config Undefined",

	ExportIsExporting:  "Exporting",
	ExportFailed:       "Export failed",
	ExportNeedBirthday: "Birthday information is missing. Please fill in your birthday in the member information first.",

	BusinessLogicCreditPrefix:          "Card number is not eligible for use",
	BusinessLogicLoyaltyFormat:         "The hotel loyalty card number format does not match",
	BusinessLogicBooking:               "Hotel booking error",
	BusinessLogicOrderStatus:           "Order status error",
	BusinessLogicProfileLosingPassport: "Personal profile is missing passport name",
	BusinessLogicEmailDomain:           "Email domain is not qualified",
	BusinessLogicFunctionNotAllow:      "This function is not enabled",
	BusinessLogicExist:                 "This information already exists",
	BusinessLogicValidationError:       "Data validation error",
	BusinessLogicNotAllowOrgUser:       "Users not allowed to operate this organization",

	BusinessLogicHotelBookingTestMode:               "Develop Test Mode",
	BusinessLogicHotelBookingDebugMode:              "Debug Mode",
	BusinessLogicHotelBookingCreateRecordFailed:     "Create booking record failed",
	BusinessLogicHotelBookingCreditCardTypeUnknown:  "Unrecognized credit card type",
	BusinessLogicHotelBookingNotFoundHotel:          "Can't find this hotel",
	BusinessLogicHotelBookingSSRHaveInvalidKey:      "Unrecognized special request key",
	BusinessLogicHotelBookingSSRSGGroupMaxLength:    "Shangri-La Group allow only 1 Special request",
	BusinessLogicHotelBookingSSRMaxLength:           "Special request only a maximum of 4 is allowed",
	BusinessLogicHotelBookingFailed:                 "Booking Failed",
	BusinessLogicHotelBookingUnknown:                "Booking unknown error",
	BusinessLogicHotelBookingNotSupportChild:        "This rate not support children",
	BusinessLogicHotelBookingOverQtyMonth:           "This month's reservation quota is full",
	BusinessLogicHotelBookingNotSupportOver1Billion: "Reservations exceeding 1 billion (local currency) are currently not supported",
	BusinessLogicHotelBookingNoQuota:                "This month's hotel booking quota has been used up",
	BusinessLogicHotelBookingSSRChildrenMaxLength:   "Special request only a maximum of 3 is allowed",
	BusinessLogicHotelBookingCancelNotAllow:         "This order has exceeded the time limit or the status does not allow cancellation, so it cannot be canceled.",
	BusinessLogicHotelBookingPriceMaintain:          "The information of this plan is under maintenance and reservations are not available at the moment.",
	BusinessLogicHotelBookingDailyPriceNotComplete:  "The daily room rate information for this date range for this plan is incomplete and reservations are not available at the moment.",
	BusinessLogicHotelBookingPrice:                  "The price for this plan may be incorrect",
	BusinessLogicHotelBookingFormRequired:           "For Platinum and Agent members, the form information is required but there are gaps, please check and submit again.",

	BusinessLogicProductIsNotPublished: "Product is not published",

	BusinessLogicOrderIsNotShow:                         "Order is not show",
	BusinessLogicOrderAlreadyPaid:                       "Order is already paid",
	BusinessLogicOrderAmountTooLow:                      "Order amount is too low",
	BusinessLogicOrderExchangeRateIsZero:                "Order exchange rate is zero",
	BusinessLogicOrderReachPickupLimitOfPeople:          "Order reach pickup limit of people",
	BusinessLogicOrderPickupTimeIsTooLate:               "Order pickup time is too late",
	BusinessLogicOrderFastTrackTimeIsUnavailable:        "Order fast track time is unavailable",
	BusinessLogicOrderFastTrackTimeIsTooClose:           "Order fast track time is too close",
	BusinessLogicOrderUserNotMatch:                      "Order user not match",
	BusinessLogicOrderOperatorNotMatch:                  "Order operator not match",
	BusinessLogicOrderOperatorIsEmpty:                   "Order operator is empty",
	BusinessLogicOrderOperatorAlreadyExist:              "Order operator already exist",
	BusinessLogicOrderStatusCouldNotBeCancelled:         "Order status could not be cancelled",
	BusinessLogicOrderStatusNotMatch:                    "Order status not match",
	BusinessLogicOrderStartDateMustBeEarlierThanEndDate: "Start date must be earlier than end date",
	BusinessLogicOrderUserIdCouldNotBeEmpty:             "Order user id could not be empty",
	BusinessLogicOrderAmountMustBeGreaterThanZero:       "Order amount must be greater than zero",
	BusinessLogicOrderTailorMadeNotFound:                "Tailor made not found",
	BusinessLogicOrderTailorMadeStatusCanNotBeCancelled: "Tailor made status can not be cancelled",

	BusinessLogicOrderTradePreviousNotPaid:                    "The previous order trade has not been paid",
	BusinessLogicOrderTradePaymentCategoryNotMatch:            "Payment category does not match",
	BusinessLogicOrderTradePaymentUnknownStatus:               "Unknown payment status",
	BusinessLogicOrderTradeExternalIdentityCodeNotMatch:       "External identity code does not match",
	BusinessLogicOrderTradePaymentAmountMustBeGreaterThanZero: "Payment amount must be greater than zero",
	BusinessLogicOrderTradePaymentAmountNotMatch:              "Payment amount does not match",
	BusinessLogicOrderTradePaymentNotAllowToModify:            "Payment not allow to modify",
	BusinessLogicOrderTradePaymentRefundFail:                  "Payment refund fail",

	BusinessLogicTailorMadeStartDateMustBeEarlierThanEndDate: "Start date must be earlier than end date",
	BusinessLogicTailorMadeMembershipNeedToHighThanElite:     "Membership need to high than elite",
	BusinessLogicTailorMadeUnknownOrderType:                  "Unknown tailor made order type",
	BusinessLogicTailorMadeMembershipNeedToHighThanPlatinum:  "Membership need to high than platinum",

	BusinessLogicHotBlogNumberReachLimit:         "Hot blog number reach the limit",
	BusinessLogicBlogCategoryHasBlogCanNotDelete: "Blog category has blog can not delete",
	BusinessLogicBlogTagHasBlogCanNotDelete:      "Blog tag has blog can not delete",
	BusinessLogicBlogUrlExist:                    "The url is exist",
	BusinessLogicBlogNameExist:                   "The name is exist",

	BusinessLogicAgentReportRemittedCannotBeDeleted:                  "Remitted report cannot be deleted",
	BusinessLogicAgentReportRemittedCannotBeUpdated:                  "Remitted report cannot be updated",
	BusinessLogicHotelOrderCommissionIncomeNotFound:                  "Hotel order commission income not found",
	BusinessLogicOrderCommissionIncomeIsShareRemitted:                "Order commission income is share remitted",
	BusinessLogicOrderCommissionIncomeOrderMustBeOrdered:             "Order commission income order must be ordered",
	BusinessLogicAgentCommissionReportHotelOrderMappingAlreadyExists: "Agent commission report hotel order mapping already exists",
	BusinessLogicAgentCommissionReportHotelOrderNotBelongToAgent:     "Hotel order not belong to agent",
	BusinessLogicAgentCommissionReportAlreadyRemitted:                "Agent commission report already remitted",
	BusinessLogicAgentCommissionHotelOrderAlreadyInReport:            "Hotel order already in agent commission report, cannot be updated",
}

// MessageZhTW 繁中錯誤訊息
var MessageZhTW = map[string]string{
	Authorized:    "權限錯誤",
	Validation:    "資料驗證錯誤",
	NotFound:      "查無資料",
	NotChange:     "沒有變更",
	Database:      "資料庫錯誤",
	Redis:         "快取錯誤",
	BusinessLogic: "商業邏輯錯誤",
	Permission:    "權限不足",
	Parse:         "文字解析錯誤",
	CallApi:       "服務呼叫錯誤",
	Notify:        "通知錯誤",
	Export:        "匯出錯誤",
	Unknown:       "非預期錯誤",

	AuthorizedUnLogin:             "請先登入",
	AuthorizedUserOrPassWrong:     "帳號或密碼有誤",
	AuthorizedOrgAppRequired:      "缺少必要參數 Org/App",
	AuthorizedAppNotActivated:     "該組織App未開通",
	AuthorizedAppNotExist:         "該組織App不存在",
	AuthorizedMainServiceNotReady: "主服務未啟用或無法通訊",
	AuthorizedServiceMaintainMode: "系統維護中",
	AuthorizedOrgNotExist:         "該組織不存在",

	AuthorizedAccountNotExist:           "帳號不存在",
	AuthorizedAccountNotInService:       "帳號不在此服務中",
	AuthorizedTokenInvalid:              "Token 不合法",
	AuthorizedAccountIsUsed:             "該帳號已被使用",
	AuthorizedAccountNoRule:             "該帳號未綁定角色",
	AuthorizedAccountBlackList:          "帳號暫時鎖定中",
	AuthorizedAccountReqToManyTime:      "驗證太過頻繁",
	AuthorizedRefreshTokenInvalid:       "Refresh Token 不合法",
	AuthorizedRefreshTokenExpired:       "Refresh Token 時效過期",
	AuthorizedWrongPasswordTooManyTimes: "密碼驗證失敗太多次，請稍候在試",

	AuthorizedCheckVerifyCodeExpired:    "驗證碼已過期",
	AuthorizedCheckVerifyCodeNoSend:     "驗證碼並無發送",
	AuthorizedCheckVerifyCodeWrong:      "驗證碼錯誤",
	AuthorizedCheckVerifyCodeTooFast:    "重新發送時間太短",
	AuthorizedCheckVerifyCodeHasNotSent: "驗證碼尚未發送",
	AuthorizedCheckVerifyInMin:          "超過驗證時間，驗證碼過期，請重新取得驗證信",

	PermissionNotAllowService: "該帳號無此權限",

	ParseImageConversionFailed: "圖片轉檔失敗",

	ValidationEqField:               "%s 必須與 %s 相同",
	ValidationIp:                    "%s 必須為 IP 格式",
	ValidationAlpha:                 "%s 必須為英文",
	ValidationAlphaNum:              "%s 必須為英數",
	ValidationBoolean:               "%s 必須為布林值",
	ValidationContains:              "%s 必須包含關鍵字 %s",
	ValidationContainsAny:           "%s 必須包含任何關鍵字",
	ValidationExcludes:              "%s 必須排除關鍵字 %s",
	ValidationStartsNotWith:         "%s 必須不以 %s 開頭",
	ValidationStartsWith:            "%s 必須以 %s 開頭",
	ValidationEndsNotWith:           "%s 必須不以 %s 結尾",
	ValidationEndsWith:              "%s 必須以 %s 結尾",
	ValidationLowercase:             "%s 必須為英文全小寫",
	ValidationUppercase:             "%s 必須為英文全大寫",
	ValidationNumber:                "%s 必須為純數字",
	ValidationNumeric:               "%s 必須為純數字",
	ValidationBase64:                "%s 必須為 base64 編碼",
	ValidationDatetime:              "%s 日期格式錯誤(%s)",
	ValidationEmail:                 "%s 必須為 email 格式",
	ValidationJson:                  "%s 必須為 JSON 格式",
	ValidationJwt:                   "%s 必須為 JWT 格式",
	ValidationLatitude:              "%s 必須為經度坐標",
	ValidationLongitude:             "%s 必須為經度坐標",
	ValidationUuid:                  "%s 必須為 UUID 格式",
	ValidationMd5:                   "%s 必須為 MD5 格式",
	ValidationEq:                    "%s 應等於 %s",
	ValidationNe:                    "%s 應不等於 %s",
	ValidationGt:                    "%s 數值過小",
	ValidationGte:                   "%s 數值過小",
	ValidationLt:                    "%s 數值過大",
	ValidationLte:                   "%s 數值過大",
	ValidationLen:                   "%s 長度必須等於 %s",
	ValidationMax:                   "%s 最大長度不得超過 %s",
	ValidationMin:                   "%s 最小長度不得低於 %s",
	ValidationRequired:              "%s 為必填",
	ValidationOneOf:                 "%s 必須為定義值之一",
	ValidationRequiredIf:            "%s 在 %s 存在時為必填",
	ValidationReCaptcha:             "reCaptcha 驗證錯誤",
	ValidationTypeInvalid:           "欄位類型不合法",
	ValidationPasswordNotAllowBlank: "密碼不允許空格",
	ValidationPasswordLen:           "密碼長度過短",

	ValidationRequestDuplicated: "重複請求",
	ValidationUndefined:         "%s 格式有誤",

	ValidationOrderId:            "%s 訂單編號格式錯誤",
	ValidationYmd:                "%s 日期格式錯誤(2000-01-01)",
	ValidationPassword:           "%s 密碼格式錯誤",
	ValidationFormat:             "%s 資料類型必須為 %s",
	ValidationBodyRequired:       "請求的 Body 資料不得為空",
	ValidationFileExtension:      "不合法的副檔名",
	ValidationPagingSortNotExist: "指定排序的欄位不存在",

	DatabaseSelect:          "資料庫 select 錯誤",
	DatabaseInsert:          "資料庫 insert 錯誤",
	DatabaseInsertOrUpdate:  "資料庫 insertUpdate 錯誤",
	DatabaseUpdate:          "資料庫 update 錯誤",
	DatabaseDelete:          "資料庫 delete 錯誤",
	DatabaseSave:            "資料庫 save 錯誤",
	DatabaseSaveOrUpdate:    "資料庫 save 或 update 錯誤",
	DatabaseTransaction:     "資料庫 transaction 錯誤",
	DatabaseCreateInBatches: "資料庫 CreateInBatches 錯誤",
	DatabaseGenerateId:      "資料庫 Generate Id 錯誤",
	DatabaseConnect:         "資料庫連線失敗",

	ConfigUndefined: "參數未配置",

	ExportIsExporting:  "正在匯出中",
	ExportFailed:       "匯出失敗",
	ExportNeedBirthday: "缺少生日資訊，請先至會員資料填寫自己的生日",

	BusinessLogicCreditPrefix:          "卡號使用資格不符",
	BusinessLogicLoyaltyFormat:         "酒店會籍卡號格式不符",
	BusinessLogicBooking:               "酒店預訂錯誤",
	BusinessLogicOrderStatus:           "訂單狀態錯誤",
	BusinessLogicProfileLosingPassport: "個人資料缺少護照姓名",
	BusinessLogicEmailDomain:           "email domain 資格不符",
	BusinessLogicFunctionNotAllow:      "該功能未開通",
	BusinessLogicExist:                 "該資料已經存在",
	BusinessLogicValidationError:       "資料驗證有誤",
	BusinessLogicNotAllowOrgUser:       "不允許此組織的 User 操作此功能",

	BusinessLogicHotelBookingTestMode:               "開發人員 Test 模式",
	BusinessLogicHotelBookingDebugMode:              "Debug 模式",
	BusinessLogicHotelBookingCreateRecordFailed:     "儲存訂單失敗",
	BusinessLogicHotelBookingCreditCardTypeUnknown:  "無法辨識信用卡類型",
	BusinessLogicHotelBookingNotFoundHotel:          "查無此酒店",
	BusinessLogicHotelBookingSSRHaveInvalidKey:      "無法辨識此特別備註",
	BusinessLogicHotelBookingSSRSGGroupMaxLength:    "香格里拉集團只能填寫 1 組特別備註",
	BusinessLogicHotelBookingSSRMaxLength:           "特別備註最多只能填寫 4 組",
	BusinessLogicHotelBookingFailed:                 "酒店預訂失敗",
	BusinessLogicHotelBookingUnknown:                "酒店預訂非預期錯誤",
	BusinessLogicHotelBookingNotSupportChild:        "此酒店方案皆以成人計算，不支援小孩名額",
	BusinessLogicHotelBookingOverQtyMonth:           "本月預訂額度已滿",
	BusinessLogicHotelBookingNotSupportOver1Billion: "目前不支援超過 10 億額度的預訂(當地貨幣)",
	BusinessLogicHotelBookingNoQuota:                "本月酒店預訂額度已用完",
	BusinessLogicHotelBookingSSRChildrenMaxLength:   "特別備註最多只能填寫 3 組",
	BusinessLogicHotelBookingCancelNotAllow:         "此訂單超過時限、或狀態不允許取消，故不可取消",
	BusinessLogicHotelBookingPriceMaintain:          "此方案資料維護中，暫不提供預訂",
	BusinessLogicHotelBookingDailyPriceNotComplete:  "此方案該日期區間的每日房價資訊不完整，暫不提供預訂",
	BusinessLogicHotelBookingPrice:                  "此方案價格可能有誤",
	BusinessLogicHotelBookingFormRequired:           "Platinum、Agent 會員，表單資料為必填但有缺漏，請檢查後再次送出",

	BusinessLogicProductIsNotPublished: "產品未發佈",

	BusinessLogicOrderIsNotShow:                         "此訂單為隱藏狀態，暫無法付款，請聯絡客服",
	BusinessLogicOrderAlreadyPaid:                       "訂單已過付款",
	BusinessLogicOrderAmountTooLow:                      "訂單金額過低",
	BusinessLogicOrderExchangeRateIsZero:                "訂單匯率為零",
	BusinessLogicOrderReachPickupLimitOfPeople:          "訂單達到接送人數上限",
	BusinessLogicOrderPickupTimeIsTooLate:               "訂單接送時間過晚",
	BusinessLogicOrderFastTrackTimeIsUnavailable:        "訂單快速通關時間不可用",
	BusinessLogicOrderFastTrackTimeIsTooClose:           "訂單快速通關時間過近",
	BusinessLogicOrderUserNotMatch:                      "訂單使用者不符",
	BusinessLogicOrderOperatorNotMatch:                  "訂單操作者不符",
	BusinessLogicOrderOperatorIsEmpty:                   "訂單操作者為空",
	BusinessLogicOrderOperatorAlreadyExist:              "訂單操作者已存在",
	BusinessLogicOrderStatusCouldNotBeCancelled:         "訂單狀態無法取消",
	BusinessLogicOrderStatusNotMatch:                    "訂單狀態不符",
	BusinessLogicOrderStartDateMustBeEarlierThanEndDate: "開始日期必須早於結束日期",
	BusinessLogicOrderUserIdCouldNotBeEmpty:             "訂單使用者 ID 不可為空",
	BusinessLogicOrderAmountMustBeGreaterThanZero:       "訂單金額必須大於零",
	BusinessLogicOrderTailorMadeNotFound:                "找不到訂單對應的私人訂製",
	BusinessLogicOrderTailorMadeStatusCanNotBeCancelled: "訂單對應的私人訂製狀態不可為取消狀態",

	BusinessLogicOrderTradePreviousNotPaid:                    "前一筆訂單款項尚未付款",
	BusinessLogicOrderTradePaymentCategoryNotMatch:            "付款類別不符",
	BusinessLogicOrderTradePaymentUnknownStatus:               "未知付款狀態",
	BusinessLogicOrderTradeExternalIdentityCodeNotMatch:       "交易外部識別碼不符",
	BusinessLogicOrderTradePaymentAmountMustBeGreaterThanZero: "付款金額必須大於零",
	BusinessLogicOrderTradePaymentAmountNotMatch:              "付款金額不符",
	BusinessLogicOrderTradePaymentNotAllowToModify:            "該付款不允許修改",
	BusinessLogicOrderTradePaymentRefundFail:                  "該付款退款失敗",

	BusinessLogicTailorMadeStartDateMustBeEarlierThanEndDate: "開始日期必須早於結束日期",
	BusinessLogicTailorMadeMembershipNeedToHighThanElite:     "會籍需高於 Elite 會籍",
	BusinessLogicTailorMadeUnknownOrderType:                  "未知私人訂製訂單類型",
	BusinessLogicTailorMadeMembershipNeedToHighThanPlatinum:  "會籍需高於 Platinum 會籍",

	BusinessLogicHotBlogNumberReachLimit:         "熱門部落格數量已達上限",
	BusinessLogicBlogCategoryHasBlogCanNotDelete: "部落格分類下有部落格無法刪除",
	BusinessLogicBlogTagHasBlogCanNotDelete:      "部落格標籤下有部落格無法刪除",
	BusinessLogicBlogUrlExist:                    "網址已存在",
	BusinessLogicBlogNameExist:                   "名稱已存在",

	BusinessLogicAgentReportRemittedCannotBeDeleted:                  "已撥款報表無法刪除",
	BusinessLogicAgentReportRemittedCannotBeUpdated:                  "已撥款報表無法更新",
	BusinessLogicHotelOrderCommissionIncomeNotFound:                  "酒店訂單未認列佣金",
	BusinessLogicOrderCommissionIncomeIsShareRemitted:                "訂單分銷佣金已匯出，無法修改",
	BusinessLogicOrderCommissionIncomeOrderMustBeOrdered:             "訂單分銷佣金訂單必須為預定成功狀態",
	BusinessLogicAgentCommissionReportHotelOrderMappingAlreadyExists: "訂單已經有對應的報表",
	BusinessLogicAgentCommissionReportHotelOrderNotBelongToAgent:     "酒店訂單不屬於該代理商",
	BusinessLogicAgentCommissionReportAlreadyRemitted:                "該報表已撥款",
	BusinessLogicAgentCommissionHotelOrderAlreadyInReport:            "酒店訂單已經在報表中，無法更新",
}

// MessageZhCN 簡中錯誤訊息
var MessageZhCN = map[string]string{
	Authorized:    "权限错误",
	Validation:    "资料验证错误",
	NotFound:      "查无资料",
	NotChange:     "没有改变",
	Database:      "资料库错误",
	Redis:         "快取错误",
	BusinessLogic: "商业逻辑错误",
	Permission:    "权限不足",
	Parse:         "解析错误",
	CallApi:       "服务呼叫错误",
	Notify:        "通知错误",
	Export:        "汇出错误",
	Unknown:       "非预期错误",

	AuthorizedUnLogin:             "请先登入",
	AuthorizedUserOrPassWrong:     "帐号或密码有误",
	AuthorizedOrgAppRequired:      "缺少必要参数 Org/App",
	AuthorizedAppNotActivated:     "该组织App未开通",
	AuthorizedAppNotExist:         "该组织App不存在",
	AuthorizedMainServiceNotReady: "主服务未启用或无法通讯",
	AuthorizedServiceMaintainMode: "系统维护中",
	AuthorizedOrgNotExist:         "该组织不存在",

	AuthorizedAccountNotExist:           "帐号不存在",
	AuthorizedAccountNotInService:       "帐号不在此服务中",
	AuthorizedTokenInvalid:              "Token 不合法",
	AuthorizedAccountIsUsed:             "该帐号已被使用",
	AuthorizedAccountNoRule:             "该帐号未绑定角色",
	AuthorizedAccountBlackList:          "帐号暂时锁定中",
	AuthorizedAccountReqToManyTime:      "验证太过频繁",
	AuthorizedRefreshTokenInvalid:       "Refresh Token 不合法",
	AuthorizedRefreshTokenExpired:       "Refresh Token 时效过期",
	AuthorizedWrongPasswordTooManyTimes: "密码验证失败太多次，请稍候在试",

	AuthorizedCheckVerifyCodeExpired:    "验证码已过期",
	AuthorizedCheckVerifyCodeNoSend:     "验证码并无发送",
	AuthorizedCheckVerifyCodeWrong:      "验证码错误",
	AuthorizedCheckVerifyCodeTooFast:    "重新发送时间太短",
	AuthorizedCheckVerifyCodeHasNotSent: "验证码尚未发送",
	AuthorizedCheckVerifyInMin:          "超过验证时间，验证码过期，请重新取得验证信",

	PermissionNotAllowService: "该帐号无此权限",

	ParseImageConversionFailed: "图片转档失败",

	ValidationEqField:               "%s 必须与 %s 相同",
	ValidationIp:                    "%s 必须为 IP 格式",
	ValidationAlpha:                 "%s 必须为英文",
	ValidationAlphaNum:              "%s 必须为英数",
	ValidationBoolean:               "%s 必须为布林值",
	ValidationContains:              "%s 必须包含关键字 %s",
	ValidationContainsAny:           "%s 必须包含任何关键字",
	ValidationExcludes:              "%s 必须排除关键字 %s",
	ValidationStartsNotWith:         "%s 必须不以 %s 开头",
	ValidationStartsWith:            "%s 必须以 %s 开头",
	ValidationEndsNotWith:           "%s 必须不以 %s 结尾",
	ValidationEndsWith:              "%s 必须以 %s 结尾",
	ValidationLowercase:             "%s 必须为英文全小写",
	ValidationUppercase:             "%s 必须为英文全大写",
	ValidationNumber:                "%s 必须为纯数字",
	ValidationNumeric:               "%s 必须为纯数字",
	ValidationBase64:                "%s 必须为 base64 编码",
	ValidationDatetime:              "%s 日期格式错误(%s)",
	ValidationEmail:                 "%s 必须为 email 格式",
	ValidationJson:                  "%s 必须为 JSON 格式",
	ValidationJwt:                   "%s 必须为 JWT 格式",
	ValidationLatitude:              "%s 必须为纬度坐标",
	ValidationLongitude:             "%s 必须为经度坐标",
	ValidationUuid:                  "%s 必须为 UUID 格式",
	ValidationMd5:                   "%s 必须为 MD5 格式",
	ValidationEq:                    "%s 应等于 %s",
	ValidationNe:                    "%s 应不等于 %s",
	ValidationGt:                    "%s 数值过小",
	ValidationGte:                   "%s 数值过小",
	ValidationLt:                    "%s 数值过大",
	ValidationLte:                   "%s 数值过大",
	ValidationLen:                   "%s 长度必须等于 %s",
	ValidationMax:                   "%s 最大长度不得超过 %s",
	ValidationMin:                   "%s 最小长度不得低于 %s",
	ValidationRequired:              "%s 为必填",
	ValidationOneOf:                 "%s 必须为定义值之一",
	ValidationRequiredIf:            "%s 在 %s 存在时为必填",
	ValidationReCaptcha:             "reCaptcha 验证错误",
	ValidationTypeInvalid:           "栏位类型不合法",
	ValidationPasswordNotAllowBlank: "密码不允许空格",
	ValidationPasswordLen:           "密码长度过短",

	ValidationRequestDuplicated: "重复请求",
	ValidationUndefined:         "%s 格式有误",

	ValidationOrderId:            "%s 订单编号格式错误",
	ValidationYmd:                "%s 日期格式错误(2000-01-01)",
	ValidationPassword:           "%s 密码格式错误",
	ValidationFormat:             "%s 资料类型必须为 %s",
	ValidationBodyRequired:       "请求的 Body 资料不得为空",
	ValidationFileExtension:      "不合法的副档名",
	ValidationPagingSortNotExist: "指定排序的栏位不存在",

	DatabaseSelect:          "数据库 select 错误",
	DatabaseInsert:          "数据库 insert 错误",
	DatabaseInsertOrUpdate:  "数据库 insertUpdate 错误",
	DatabaseUpdate:          "数据库 update 错误",
	DatabaseDelete:          "数据库 delete 错误",
	DatabaseSave:            "数据库 save 错误",
	DatabaseSaveOrUpdate:    "数据库 save 或 update 错误",
	DatabaseTransaction:     "数据库 transaction 错误",
	DatabaseCreateInBatches: "数据库 CreateInBatches 错误",
	DatabaseGenerateId:      "数据库 Generate Id 错误",
	DatabaseConnect:         "数据库 连线失败",

	ConfigUndefined: "参数未设置",

	ExportIsExporting:  "正在汇出中",
	ExportFailed:       "汇出失败",
	ExportNeedBirthday: "缺少生日资讯，请先至会员资料填写自己的生日",

	BusinessLogicCreditPrefix:          "卡号使用资格不符",
	BusinessLogicLoyaltyFormat:         "酒店会籍卡号格式不符",
	BusinessLogicBooking:               "酒店预订错误",
	BusinessLogicOrderStatus:           "订单状态错误",
	BusinessLogicProfileLosingPassport: "个人资料缺少护照姓名",
	BusinessLogicEmailDomain:           "email domain 资格不符",
	BusinessLogicFunctionNotAllow:      "该功能未开通",
	BusinessLogicExist:                 "该资料已经存在",
	BusinessLogicValidationError:       "资料验证有误",
	BusinessLogicNotAllowOrgUser:       "不允许此组织的 User 操作此功能",

	BusinessLogicHotelBookingTestMode:               "开发人员 Test 模式",
	BusinessLogicHotelBookingDebugMode:              "Debug 模式",
	BusinessLogicHotelBookingCreateRecordFailed:     "储存订单失败",
	BusinessLogicHotelBookingCreditCardTypeUnknown:  "无法辨识信用卡类型",
	BusinessLogicHotelBookingNotFoundHotel:          "查无此酒店",
	BusinessLogicHotelBookingSSRHaveInvalidKey:      "无法辨识此特别备注",
	BusinessLogicHotelBookingSSRSGGroupMaxLength:    "香格里拉集团只能填写 1 组特别备注",
	BusinessLogicHotelBookingSSRMaxLength:           "特别备注最多只能填写 4 组",
	BusinessLogicHotelBookingFailed:                 "酒店预订失败",
	BusinessLogicHotelBookingUnknown:                "酒店预订非预期错误",
	BusinessLogicHotelBookingNotSupportChild:        "此酒店方案皆以成人计算，不支援小孩名额",
	BusinessLogicHotelBookingOverQtyMonth:           "本月预订额度已满",
	BusinessLogicHotelBookingNotSupportOver1Billion: "目前不支援超过 10 亿额度的预订(当地货币)",
	BusinessLogicHotelBookingNoQuota:                "本月酒店预订额度已用完",
	BusinessLogicHotelBookingSSRChildrenMaxLength:   "特别备注最多只能填写 3 组",
	BusinessLogicHotelBookingCancelNotAllow:         "此订单超过时限、或状态不允许取消，故不可取消",
	BusinessLogicHotelBookingPriceMaintain:          "此方案资料维护中，暂不提供预订",
	BusinessLogicHotelBookingDailyPriceNotComplete:  "此方案该日期区间的每日房价资讯不完整，暂不提供预订",
	BusinessLogicHotelBookingPrice:                  "此方案价格可能有误",
	BusinessLogicHotelBookingFormRequired:           "Platinum、Agent 会员，表单资料为必填但有缺漏，请检查后再次送出",

	BusinessLogicProductIsNotPublished: "产品未发布",

	BusinessLogicOrderIsNotShow:                         "此订单为隐藏状态，暂无法付款，请联络客服",
	BusinessLogicOrderAlreadyPaid:                       "订单已过付款",
	BusinessLogicOrderAmountTooLow:                      "订单金额过低",
	BusinessLogicOrderExchangeRateIsZero:                "订单汇率为零",
	BusinessLogicOrderReachPickupLimitOfPeople:          "订单达到接送人数上限",
	BusinessLogicOrderPickupTimeIsTooLate:               "订单接送时间过晚",
	BusinessLogicOrderFastTrackTimeIsUnavailable:        "订单快速通关时间不可用",
	BusinessLogicOrderFastTrackTimeIsTooClose:           "订单快速通关时间过近",
	BusinessLogicOrderUserNotMatch:                      "订单使用者不符",
	BusinessLogicOrderOperatorNotMatch:                  "订单操作者不符",
	BusinessLogicOrderOperatorIsEmpty:                   "订单操作者为空",
	BusinessLogicOrderOperatorAlreadyExist:              "订单操作者已存在",
	BusinessLogicOrderStatusCouldNotBeCancelled:         "订单状态无法取消",
	BusinessLogicOrderStatusNotMatch:                    "订单状态不符",
	BusinessLogicOrderStartDateMustBeEarlierThanEndDate: "开始日期必须早于结束日期",
	BusinessLogicOrderUserIdCouldNotBeEmpty:             "订单使用者 ID 不可为空",
	BusinessLogicOrderAmountMustBeGreaterThanZero:       "订单金额必须大于零",
	BusinessLogicOrderTailorMadeNotFound:                "找不到订单对应的私人订制",
	BusinessLogicOrderTailorMadeStatusCanNotBeCancelled: "订单对应的私人订制状态不可为取消状态",

	BusinessLogicOrderTradePreviousNotPaid:                    "前一笔订单款项尚未付款",
	BusinessLogicOrderTradePaymentCategoryNotMatch:            "付款类别不符",
	BusinessLogicOrderTradePaymentUnknownStatus:               "未知付款状态",
	BusinessLogicOrderTradeExternalIdentityCodeNotMatch:       "交易外部识别码不符",
	BusinessLogicOrderTradePaymentAmountMustBeGreaterThanZero: "付款金额必须大于零",
	BusinessLogicOrderTradePaymentAmountNotMatch:              "付款金额不符",
	BusinessLogicOrderTradePaymentNotAllowToModify:            "该付款不允许修改",
	BusinessLogicOrderTradePaymentRefundFail:                  "该付款退款失败",

	BusinessLogicTailorMadeStartDateMustBeEarlierThanEndDate: "开始日期必须早于结束日期",
	BusinessLogicTailorMadeMembershipNeedToHighThanElite:     "会籍需高于 Elite 会籍",
	BusinessLogicTailorMadeUnknownOrderType:                  "未知私人订制订单类型",
	BusinessLogicTailorMadeMembershipNeedToHighThanPlatinum:  "会籍需高于 Platinum 会籍",

	BusinessLogicHotBlogNumberReachLimit:         "热门部落格数量已达上限",
	BusinessLogicBlogCategoryHasBlogCanNotDelete: "部落格分类下有部落格无法删除",
	BusinessLogicBlogTagHasBlogCanNotDelete:      "部落格标签下有部落格无法删除",
	BusinessLogicBlogUrlExist:                    "网址已存在",
	BusinessLogicBlogNameExist:                   "名称已存在",

	BusinessLogicAgentReportRemittedCannotBeDeleted:                  "已拨款报表无法删除",
	BusinessLogicAgentReportRemittedCannotBeUpdated:                  "已拨款报表无法更新",
	BusinessLogicHotelOrderCommissionIncomeNotFound:                  "酒店订单未认列佣金",
	BusinessLogicOrderCommissionIncomeIsShareRemitted:                "订单分销佣金已汇出，无法修改",
	BusinessLogicOrderCommissionIncomeOrderMustBeOrdered:             "订单分销佣金订单必须为预定成功状态",
	BusinessLogicAgentCommissionReportHotelOrderMappingAlreadyExists: "订单已經有對應的報表",
	BusinessLogicAgentCommissionReportHotelOrderNotBelongToAgent:     "酒店订单不属於該代理商",
	BusinessLogicAgentCommissionReportAlreadyRemitted:                "该报表已拨款",
	BusinessLogicAgentCommissionHotelOrderAlreadyInReport:            "酒店订单已经在报表中，无法更新",
}

var MessageJa = map[string]string{
	Authorized:    "権限エラー",
	Validation:    "データー検証エラー",
	NotFound:      "データーなし",
	NotChange:     "変更なし",
	Database:      "資料庫エラー",
	Redis:         "Cache エラー",
	BusinessLogic: "ビジネスロジックエラー",
	Permission:    "許可不足",
	Parse:         "文字分析エラー",
	CallApi:       "サービスコールエラー",
	Notify:        "通知エラー",
	Export:        "輸出エラー",
	Unknown:       "未知エラー",

	AuthorizedUnLogin:             "認証済みログイン",
	AuthorizedUserOrPassWrong:     "ID かパスワードエラー",
	AuthorizedOrgAppRequired:      "必須パラメータが欠落しています，Org/App",
	AuthorizedAppNotActivated:     "承認されたアプリがアクティブ化されていません",
	AuthorizedAppNotExist:         "認可されたアプリが存在しない",
	AuthorizedMainServiceNotReady: "メインサービスの準備ができていません",
	AuthorizedServiceMaintainMode: "システムメンテナンス中",
	AuthorizedOrgNotExist:         "当組織が存在しません",

	AuthorizedAccountNotExist:           "ID が存在しません",
	AuthorizedAccountNotInService:       "承認されたアカウントはサービス中ではありません",
	AuthorizedTokenInvalid:              "Token 不正",
	AuthorizedAccountIsUsed:             "当 ID が既に使用されてます",
	AuthorizedAccountNoRule:             "認可済みアカウントは役割なし",
	AuthorizedAccountBlackList:          "アカウントが暫くロック中",
	AuthorizedAccountReqToManyTime:      "認証頻繁",
	AuthorizedRefreshTokenInvalid:       "Refresh Token 不合法",
	AuthorizedRefreshTokenExpired:       "Refresh Token 有効期限切れ",
	AuthorizedWrongPasswordTooManyTimes: "パスワード認証が頻繁に間違えた為暫く経ってから試してください",

	AuthorizedCheckVerifyCodeExpired:    "認証コード有効期限切れ",
	AuthorizedCheckVerifyCodeNoSend:     "認証コードが発送されてません",
	AuthorizedCheckVerifyCodeWrong:      "認証コードエラー",
	AuthorizedCheckVerifyCodeTooFast:    "暫く経ってから操作してください",
	AuthorizedCheckVerifyCodeHasNotSent: "認証コードがまだ発送されてません",
	AuthorizedCheckVerifyInMin:          "認証時間超え，認証コード有効時間切れ，新しい認証コードを発行していください",

	PermissionNotAllowService: "当アカウントは許可されていません",

	ParseImageConversionFailed:      "図フォーマット変換不可",
	ValidationEqField:               "%s は必ず %s と同じ",
	ValidationIp:                    "%s は必ず IP 形式",
	ValidationAlpha:                 "%s 必ず英文",
	ValidationAlphaNum:              "%s 必ず英数字",
	ValidationBoolean:               "%s 必ずブール値",
	ValidationContains:              "%s 必ずキーワードを含め %s",
	ValidationContainsAny:           "%s 必ず任意キーワード",
	ValidationExcludes:              "%s 必ずキーワードを除く %s",
	ValidationStartsNotWith:         "%s 初めは %s ではいけません",
	ValidationStartsWith:            "%s 初めは %s でなければなりません",
	ValidationEndsNotWith:           "%s は %s で終わってはなりません",
	ValidationEndsWith:              "%s は %s で終わる必要があります",
	ValidationLowercase:             "%s 必ず英文小文字",
	ValidationUppercase:             "%s 必ず英文大文字",
	ValidationNumber:                "%s 必ず数字",
	ValidationNumeric:               "%s 必ず数字",
	ValidationBase64:                "%s 必ず base64 のコーディング",
	ValidationDatetime:              "%s 日付形式エラー(%s)",
	ValidationEmail:                 "%s 必ず email 形式",
	ValidationJson:                  "%s 必ず JSON 形式",
	ValidationJwt:                   "%s 必ず JWT 形式",
	ValidationLatitude:              "%s 必ず経度座標が必要",
	ValidationLongitude:             "%s 必ず経度座標が必要",
	ValidationUuid:                  "%s 必ず UUID 形式",
	ValidationMd5:                   "%s 必ず MD5 形式",
	ValidationEq:                    "%s イコール %s",
	ValidationNe:                    "%s ノットイコール %s",
	ValidationGt:                    "%s 数字が低い",
	ValidationGte:                   "%s 数字が低い",
	ValidationLt:                    "%s 数字が大きい",
	ValidationLte:                   "%s 数字が大きい",
	ValidationLen:                   "%s 長さは必ずイコール %s",
	ValidationMax:                   "%s 最大長は %s を超えることはできません",
	ValidationMin:                   "%s 最小長は %s 未満であってはなりません",
	ValidationRequired:              "%s 必ず入力",
	ValidationOneOf:                 "%s は定義された値の一つでなければなりません",
	ValidationRequiredIf:            "%s は %s 存在してる時必ず入力",
	ValidationReCaptcha:             "reCaptcha 認証エラー",
	ValidationTypeInvalid:           "正確な文字を入力してください",
	ValidationPasswordNotAllowBlank: "パスワードにスペースを入力しないでください",
	ValidationPasswordLen:           "パスワードが短い",

	ValidationRequestDuplicated: "重複請求",
	ValidationUndefined:         "%s 形式エラー",

	ValidationOrderId:            "%s オーダーナンバー形式エラー",
	ValidationYmd:                "%s 日期形式エラー(2000-01-01)",
	ValidationPassword:           "%s パスワード形式エラー",
	ValidationFormat:             "%s データー形式は必ず %s",
	ValidationBodyRequired:       "請求 Body のデーターは必ず入力",
	ValidationFileExtension:      "不正なファイル",
	ValidationPagingSortNotExist: "指定された並べ替えフィールドは存在しません",

	DatabaseSelect:          "データベース select エラー",
	DatabaseInsert:          "データベース insert エラー",
	DatabaseInsertOrUpdate:  "データベース insertUpdate エラー",
	DatabaseUpdate:          "データベース update エラー",
	DatabaseDelete:          "データベース delete エラー",
	DatabaseSave:            "データベース save エラー",
	DatabaseSaveOrUpdate:    "データベース save か update エラー",
	DatabaseTransaction:     "データベース transaction エラー",
	DatabaseCreateInBatches: "データベース CreateInBatches エラー",
	DatabaseGenerateId:      "データベース Generate ID エラー",
	DatabaseConnect:         "データベース接続失敗",

	ConfigUndefined: "構成未定義",

	ExportIsExporting:  "振込中",
	ExportFailed:       "振込失敗",
	ExportNeedBirthday: "誕生日が必要，まず個人情報を完成してください",

	BusinessLogicCreditPrefix:          "このカードは使えません",
	BusinessLogicLoyaltyFormat:         "ホテル会員カード番号形式エラー",
	BusinessLogicBooking:               "ホテル予約エラー",
	BusinessLogicOrderStatus:           "オーダー状態エラー",
	BusinessLogicProfileLosingPassport: "パスポート名前を入力してください",
	BusinessLogicEmailDomain:           "email domain形式エラー",
	BusinessLogicFunctionNotAllow:      "未開通関数",
	BusinessLogicExist:                 "当データーが既に存在",
	BusinessLogicValidationError:       "データー認証エラー",
	BusinessLogicNotAllowOrgUser:       "当アプリには使われておりません",

	BusinessLogicHotelBookingTestMode:               "開発メンバー Test モード",
	BusinessLogicHotelBookingDebugMode:              "Debug モード",
	BusinessLogicHotelBookingCreateRecordFailed:     "データー保存失敗",
	BusinessLogicHotelBookingCreditCardTypeUnknown:  "カード種類が認識されません",
	BusinessLogicHotelBookingNotFoundHotel:          "ホテルは存在してません",
	BusinessLogicHotelBookingSSRHaveInvalidKey:      "当特別リクエストが認識されません",
	BusinessLogicHotelBookingSSRSGGroupMaxLength:    "シャングリ・ラホテルは 1 組スペシャルリクエストのみ承る",
	BusinessLogicHotelBookingSSRMaxLength:           "スペシャルリクエストは最大 4 組",
	BusinessLogicHotelBookingFailed:                 "ホテル予約失敗",
	BusinessLogicHotelBookingUnknown:                "予約エラー原因不明",
	BusinessLogicHotelBookingNotSupportChild:        "当ホテルプロモーション料金は大人で計算してます",
	BusinessLogicHotelBookingOverQtyMonth:           "今月の予約限度に超えてます",
	BusinessLogicHotelBookingNotSupportOver1Billion: "現在は 10 億超え現地通貨の額には満たされません",
	BusinessLogicHotelBookingNoQuota:                "今月のホテル予約限度に超えてます",
	BusinessLogicHotelBookingSSRChildrenMaxLength:   "スペシャルリクエストは最大 3 組",
	BusinessLogicHotelBookingCancelNotAllow:         "当予約は有効期間が切れ、キャンセル不可となってます",
	BusinessLogicHotelBookingPriceMaintain:          "当データーはメンテナンス中為、予約はできません",
	BusinessLogicHotelBookingDailyPriceNotComplete:  "当ケースは日期の毎日料金がまだ出てない為、予約できません",
	BusinessLogicHotelBookingPrice:                  "当ケース料金はエラー可能性あり",
	BusinessLogicHotelBookingFormRequired:           "Platinum、Agent 会員様，データー資料は全部入力し，もう一度やり直してから送ってください",
	BusinessLogicProductIsNotPublished:              "当商品はまだ発売してません",

	BusinessLogicOrderIsNotShow:                  "当オーダーは隠れてます。支払いはできません、オペレータに連絡ください",
	BusinessLogicOrderAlreadyPaid:                "当オーダーは支払い済み",
	BusinessLogicOrderAmountTooLow:               "当オーダーは金額低すぎ",
	BusinessLogicOrderExchangeRateIsZero:         "当オーダーは外為 0",
	BusinessLogicOrderReachPickupLimitOfPeople:   "当オーダーは送迎人数上限に超えてます",
	BusinessLogicOrderPickupTimeIsTooLate:        "当オーダーは送迎時間が遅すぎます",
	BusinessLogicOrderFastTrackTimeIsUnavailable: "当オーダーはVIPレーン時間に適用されません",
	BusinessLogicOrderFastTrackTimeIsTooClose:    "当オーダーはVIPレーン時間に近すぎます",
	BusinessLogicOrderUserNotMatch:               "オーダー使用者エラー",
	BusinessLogicOrderOperatorNotMatch:           "オーダー捜査者エラー",
	BusinessLogicOrderOperatorIsEmpty:            "オーダー操作者が空いてます",
	BusinessLogicOrderOperatorAlreadyExist:       "オーダー操作者が既に存在してます",
	BusinessLogicOrderStatusCouldNotBeCancelled:  "オーダーはキャンセルできません",
	BusinessLogicOrderStatusNotMatch:             "オーダー操作者がエラー",

	BusinessLogicOrderStartDateMustBeEarlierThanEndDate: "チェックイン日期は必ずチェックアウト日期より前",
	BusinessLogicOrderUserIdCouldNotBeEmpty:             "オーダー使用者 ID は必ず入力",
	BusinessLogicOrderAmountMustBeGreaterThanZero:       "オーダー金額は必ず０より高い",
	BusinessLogicOrderTailorMadeNotFound:                "オーダーナンバーが見つかりません",
	BusinessLogicOrderTailorMadeStatusCanNotBeCancelled: "オーダーメイドはキャンセル不可",

	BusinessLogicOrderTradePreviousNotPaid:                    "前のオーダーがまだ支払ってない",
	BusinessLogicOrderTradePaymentCategoryNotMatch:            "クレジットカード種類エラー",
	BusinessLogicOrderTradePaymentUnknownStatus:               "支払い状態は不明",
	BusinessLogicOrderTradeExternalIdentityCodeNotMatch:       "取引識別番号エラー",
	BusinessLogicOrderTradePaymentAmountMustBeGreaterThanZero: "支払い金額は必ず０より高い",
	BusinessLogicOrderTradePaymentAmountNotMatch:              "支払い金額エラー",
	BusinessLogicOrderTradePaymentNotAllowToModify:            "当支払いは修正できません",
	BusinessLogicOrderTradePaymentRefundFail:                  "当払い戻しは失敗しました",

	BusinessLogicTailorMadeStartDateMustBeEarlierThanEndDate: "チェックイン日期は必ずチェックアウト日期より前",
	BusinessLogicTailorMadeMembershipNeedToHighThanElite:     "会員は ELITE より高いレベル会員でなければなりません",
	BusinessLogicTailorMadeUnknownOrderType:                  "不明なオーダーメイドタイプ",
	BusinessLogicTailorMadeMembershipNeedToHighThanPlatinum:  "会員は Platinum より高いレベル会員でなければなりません",

	BusinessLogicHotBlogNumberReachLimit:         "ブログ数が上限に経ってます",
	BusinessLogicBlogCategoryHasBlogCanNotDelete: "ブログの内容が書かれてる為削除不可",
	BusinessLogicBlogTagHasBlogCanNotDelete:      "ブログのタグがついてる為削除不可",
	BusinessLogicBlogUrlExist:                    "URL は既に存在してます",
	BusinessLogicBlogNameExist:                   "名前は既に存在してます",

	BusinessLogicAgentReportRemittedCannotBeDeleted:                  "支払い済データが削除不可",
	BusinessLogicAgentReportRemittedCannotBeUpdated:                  "支払い済データが更新不可",
	BusinessLogicHotelOrderCommissionIncomeNotFound:                  "ホテルのコミッションはまだ確認取れません",
	BusinessLogicOrderCommissionIncomeIsShareRemitted:                "オーダーコミッションは既に振込済み，修正できません",
	BusinessLogicOrderCommissionIncomeOrderMustBeOrdered:             "コミッションは必ず予約済みの BOOKING でなければなりません",
	BusinessLogicAgentCommissionReportHotelOrderMappingAlreadyExists: "当予約のコミッションは既に確定しました",
	BusinessLogicAgentCommissionReportHotelOrderNotBelongToAgent:     "当ホテルの予約はこの当エージェントではありません",
	BusinessLogicAgentCommissionReportAlreadyRemitted:                "当レポートは既に支払い済み",
	BusinessLogicAgentCommissionHotelOrderAlreadyInReport:            "ホテル予約は既にレポートに記載されてる為、更新できません",
}

// Internal 內部錯誤，不會顯示訊息細節
var Internal = []string{
	Parse,
	CallApi,
	Database,
	Unknown,
	AuthorizedAccountNotInService,
	AuthorizedTokenInvalid,
	AuthorizedUserOrPassWrong,
	DatabaseSelect,
	DatabaseInsert,
	DatabaseInsertOrUpdate,
	DatabaseUpdate,
	DatabaseDelete,
	DatabaseSave,
	DatabaseSaveOrUpdate,
	DatabaseTransaction,
	DatabaseCreateInBatches,
	DatabaseGenerateId,
	Config,
	ConfigUndefined,

	Redis,
}

// NeedErrorLog 必須印到 error log
var NeedErrorLog = []string{
	Parse,
	Redis,
	CallApi,
	Database,
	Unknown,
	AuthorizedAccountNotExist,
	AuthorizedAccountNotInService,
	AuthorizedAccountNoRule,
	AuthorizedOrgAppRequired,
	DatabaseSelect,
	DatabaseInsert,
	DatabaseInsertOrUpdate,
	DatabaseUpdate,
	DatabaseDelete,
	DatabaseSave,
	DatabaseSaveOrUpdate,
	DatabaseTransaction,
	DatabaseCreateInBatches,
	DatabaseGenerateId,
	Config,
	ConfigUndefined,
}

// NeedWarnLog 必須印到 warn log
var NeedWarnLog = []string{
	AuthorizedTokenInvalid,
	Permission,
	Redis,
}
