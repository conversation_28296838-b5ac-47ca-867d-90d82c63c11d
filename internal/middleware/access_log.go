package middleware

import (
	"bytes"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/claim"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/log_topic"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/loki"
	"github.com/gin-gonic/gin"
)

func AccessLog() gin.HandlerFunc {
	return func(c *gin.Context) {
		var buf bytes.Buffer
		tee := io.TeeReader(c.Request.Body, &buf)
		body, _ := ioutil.ReadAll(tee)
		c.Request.Body = ioutil.NopCloser(&buf)

		type accessLog struct {
			ClientIP string
			Method   string
			Path     string
			Referrer string
			Body     string
			Header   http.Header
		}

		header := c.Request.Header.Clone()
		header.Del("Authorization")
		ip := header.Get("X-Forwarded-For")
		if ip == "" {
			ip = c.RemoteIP()
		}

		traceId := header.Get("Trace-Id")
		if c.GetString(claim.TraceId) != "" {
			traceId = c.GetString(claim.TraceId)
		}

		loki.Log(loki.LokiLog{Level: loki.INFO, Topic: log_topic.AccessLog, Subtitle: fmt.Sprintf("%s %s", c.Request.Method, c.Request.RequestURI), TraceId: traceId, Data: accessLog{
			ClientIP: ip, // TODO: 需驗證 reverse proxy & cloudflare
			Method:   c.Request.Method,
			Path:     c.Request.RequestURI,
			//Referrer: c.Request.Referer(),
			Header: header,
			Body:   string(body),
		}})

		c.Next()
	}
}
