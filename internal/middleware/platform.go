package middleware

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	go_platform "bitbucket.org/actechinc/go-platform-golang-sdk"
	"bitbucket.org/actechinc/wota_be/pkg/wota_be"
	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/claim"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/log_topic"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/status"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto/common"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/loki"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
)

func Platform() gin.HandlerFunc {
	return func(c *gin.Context) {
		if tool.CaseInsensitiveContains(c.FullPath(), "/swagger/") {
			c.Next()
			return
		}

		if conf.Config.Debug {
			fmt.Println("Org: ", c.<PERSON>("Org"), "App: ", c.<PERSON>eader("App"))
		}

		if c.GetHeader("Org") == "" || c.GetHeader("App") == "" {
			c.Abort()
			c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.AuthorizedOrgAppRequired}.Status(c))
			return
		}

		// TODO: 也可使用 c.GetHeader("From-Host") 來解析
		platform, err := wota_be.GetPlatform(c.GetHeader("Org"), c.GetHeader("App"))
		if err != nil {
			loki.Log(loki.LokiLog{Level: loki.ERROR, Topic: log_topic.Auth, Subtitle: "middleware-platform", Message: "wota_be main service on ready", Data: err.Error})
			c.Abort()
			c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.AuthorizedAppNotExist}.Status(c))
			return
		}
		if platform == nil {
			fmt.Println("Org/App:", c.GetHeader("Org"), c.GetHeader("App"))
			c.Abort()
			c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.AuthorizedAppNotExist}.Status(c))
			return
		}
		// 開發者模式沒開啟，就不允許 ApiSecret 通過
		if c.GetBool(claim.FromApi) && !platform.IsDevelop {
			c.Abort()
			c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Permission}.Status(c))
			return
		}

		go_platform.InitConfig(conf.Config.PlatEndpoint, platform.PlatClientID, platform.PlatClientSecret, platform.Organization, platform.Application)

		c.Set(claim.Org, platform.Organization)
		c.Set(claim.App, platform.Application)
		c.Set(claim.PlatClientId, platform.PlatClientID)
		c.Set(claim.PlatClientSecret, platform.PlatClientSecret)
		c.Set(claim.AppName2Words, platform.OrgName2Words)
		if platform.OrgName2Words == "" {
			c.Abort()
			c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.AuthorizedAppNotExist, Message: "OrgName2Words undefined"}.Status(c))
			return
		}

		c.Next()
	}
}
