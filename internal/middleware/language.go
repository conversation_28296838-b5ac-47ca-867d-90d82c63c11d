package middleware

import (
	"strings"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/claim"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/language"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"github.com/gin-gonic/gin"
)

func Language() gin.HandlerFunc {
	return func(c *gin.Context) {
		var lang string // TODO: 之後要從 go-platform token 中解析
		// TODO: PHP前台，目前未切帳號，先從 header 傳送
		if c.GetHeader("Lang") != "" {
			if tool.InArray(strings.ToLower(c.GetHeader("Lang")), []string{language.En, language.ZhTwLower, language.ZhCnLower}) {
				lang = c.GetHeader("Lang")
			}
		}
		if lang == "" {
			c.Set(claim.IsDefaultLang, true)
			lang = "en"
		}
		c.Set(claim.Lang, tool.FormatLangCode(lang))

		//lang := "zh-TW" // TODO: 之後要從 go-platform token 中解析
		//// TODO: PHP前台，目前未切帳號，先從 header 傳送
		//if (strings.ToLower(c.GetString(claim.Org)) == "wota" && tool.InArray(role.Guest, introspect.Roles)) || isFromAPI {
		//	if c.GetHeader("Lang") != "" {
		//		lang, err = tool.RegularLangCode(c.GetHeader("Lang"))
		//		if err != nil {
		//			// FIXME: 需加到 resp status
		//			fmt.Errorf("lose lang")
		//		}
		//	}
		//}

		c.Next()
	}
}
