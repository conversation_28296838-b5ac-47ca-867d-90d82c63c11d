package middleware

import (
	"fmt"
	"strings"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/env"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"github.com/swaggo/swag"

	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
)

func Swagger(swaggerInfo *swag.Spec) gin.HandlerFunc {
	return func(c *gin.Context) {
		if tool.CaseInsensitiveContains(c.FullPath(), "/swagger/") {
			if c.GetHeader("From-Http-Host") == "" {
				log.Error().Msg("header lose From-Http-Host")
				c.Next()
				return
			}

			swaggerInfo.BasePath = "/v1/crawler/"

			swaggerInfo.Host = c.GetHeader("From-Http-Host")
			swaggerInfo.Title = fmt.Sprintf("%s Crawler Service", strings.ToUpper(c.<PERSON>eader("Org")))
			swaggerInfo.Description = fmt.Sprintf("Environment: <b>%s</b>\n", strings.ToUpper(conf.Config.ENV))

			swaggerInfo.Description += fmt.Sprintf("<a href=\"//%s/swagger/index.html#/\">Be Swagger</a>\n", c.GetHeader("From-Http-Host"))
			swaggerInfo.Description += fmt.Sprintf("<a href=\"//%s/swagger/tp/index.html#/\">Hotel Swagger</a>\n", c.GetHeader("From-Http-Host"))
			if c.GetHeader("Is-Develop") != "1" { // 暫時解法
				swaggerInfo.Description += fmt.Sprintf("<a href=\"//%s/swagger/product/index.html#/\">Product Swagger</a>\n", c.GetHeader("From-Http-Host"))
				swaggerInfo.Description += fmt.Sprintf("<a href=\"//%s/swagger/payment/index.html#/\">Payment Swagger</a>\n", c.GetHeader("From-Http-Host"))
			}
			swaggerInfo.Description += fmt.Sprintf("<a href=\"//%s/swagger/exchange/index.html#/\">Exchange Swagger</a>\n", c.GetHeader("From-Http-Host"))
			swaggerInfo.Description += fmt.Sprintf("<a href=\"//%s/swagger/media/index.html#/\">Media Swagger</a>\n", c.GetHeader("From-Http-Host"))

			if c.GetHeader("Is-Develop") == "1" { // 暫時解法
				var envUrl string
				switch conf.Config.ENV {
				case env.DEV:
					envUrl = "dev-"
				case env.UAT:
					envUrl = "uat-"
				}
				swaggerInfo.Description += fmt.Sprintf("<a href=\"//%sadmin-%s.wotaluxe.com/\" target=\"_blank\">系統管理員後台</a>\n", envUrl, c.GetHeader("Org"))
			}

			c.Next()
			return
		}
		c.Next()
	}
}
