package middleware

import (
	"fmt"
	"net/http"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/claim"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/status"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto/common"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"github.com/gin-gonic/gin"
)

// Permissions 必須完全符合 (在 router group 上下位置有意義，請勿移動！
func Permissions(requires []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		for _, p := range requires {
			if !tool.InArray(p, c.GetStringSlice(claim.Permissions)) {
				c.Abort()
				c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Permission, Error: fmt.Errorf("need %s permission", p)}.Status(c))
				return
			}
		}

		c.Next()
	}
}

// PermissionsOptional 任一符合 (在 router group 上下位置有意義，請勿移動！
func PermissionsOptional(requires []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		for _, p := range requires {
			if tool.InArray(p, c.GetStringSlice(claim.Permissions)) {
				c.Next()
				return
			}
		}
		c.Abort()
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Permission, Error: fmt.Errorf("need %s permission", c.GetStringSlice(claim.Permissions))}.Status(c))
	}
}
