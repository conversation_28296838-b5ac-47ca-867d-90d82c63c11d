package middleware

import (
	"net/http"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/claim"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/status"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto/common"
	"github.com/gin-gonic/gin"
)

func AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !c.GetBool(claim.IsLogin) {
			c.Abort()
			c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.AuthorizedUnLogin}.Status(c))
			return
		}
		c.Next()
	}
}
