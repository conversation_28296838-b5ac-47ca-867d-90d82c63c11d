package middleware

import (
	"fmt"
	"net/http"
	"strings"

	go_platform "bitbucket.org/actechinc/go-platform-golang-sdk"
	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/develop"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/permission"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/role"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/loki"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"github.com/rs/zerolog/log"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/cache"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/claim"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/status"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto/common"
	"github.com/gin-gonic/gin"
)

func ParseToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(claim.IsLogin, false)
		auth := c.GetHeader("Authorization")
		// 如果有 Token 或 ApiKey 就解析
		if len(auth) > 0 {
			var introspect *go_platform.TokenClaims
			var err error
			var token string
			var errCode string
			var isFromAPI = false
			var isApiTestMode = false
			if tool.InArray(auth[0:2], []string{develop.EnvTestPrefix, develop.EnvProdPrefix}) {
				isFromAPI = true
				if tool.InArray(auth[0:2], []string{develop.EnvTestPrefix}) {
					isApiTestMode = true
				}
			}
			if isFromAPI {
				token = auth
				introspect, err, errCode = cache.GetSecretIntrospect(auth)
				if err != nil {
					log.Error().Str("Topic", "AuthRequired").Str("Subtitle", "GetSecretIntrospect").Err(err).Send()
					loki.Log(loki.LokiLog{Level: loki.ERROR, Topic: "AuthRequired", Subtitle: "GetSecretIntrospect", Data: err.Error()})
				}
				if errCode != "" {
					log.Error().Str("Topic", "AuthRequired").Str("Subtitle", "GetSecretIntrospect"+errCode).Err(err).Send()
					c.Abort()
					c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: errCode}.Status(c)) // FIXME: 這邊的錯誤代碼應該用 status 中的
					return
				}
			} else {
				tmp := strings.Split(auth, "Bearer ")
				if len(tmp) < 2 {
					c.Abort()
					c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.AuthorizedTokenInvalid}.Status(c))
					return
				}
				token = tmp[1]
				introspect, err = cache.GetTokenIntrospect(token)
				// TODO: ??
				go_platform.InitConfig(conf.Config.PlatEndpoint, c.GetString(claim.PlatClientId), c.GetString(claim.PlatClientSecret), c.GetString(claim.Org), c.GetString(claim.App))
			}

			// TODO: 這邊細節要 log & response 清楚
			if err != nil {
				c.Abort()
				c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.AuthorizedTokenInvalid, Error: err}.Log().Status(c))
				return
			} else if introspect.Active == true {
				userId, err := cache.GetUserId(c.GetString(claim.Org), introspect.UserName)
				if err != nil {
					log.Error().Str("Topic", "AuthRequired").Str("Subtitle", "GetUserId").Err(err).Send()
					c.Abort()
					c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.AuthorizedAccountNotInService, Error: err}.Status(c))
					return
				}

				if len(introspect.Roles) == 0 {
					c.Abort()
					c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.AuthorizedAccountNoRule, Error: err}.Status(c))
					return
				}
				if tool.InArray(role.Black, introspect.Roles) {
					c.Abort()
					c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.AuthorizedAccountBlackList, Error: err}.Status(c))
					return
				}

				// 如果外部沒有從 header 送 lang，就從 User 身上拿
				if c.GetBool(claim.IsDefaultLang) {
					user, _ := cache.GetUser(c.GetString(claim.Org), introspect.UserName)
					if user != nil && user.Lang != "" {
						c.Set(claim.Lang, tool.FormatLangCode(user.Lang))
					}
				}

				fmt.Println("username:", introspect.UserName)
				fmt.Println("role:", "Member")
				c.Set(claim.Authorization, auth)
				c.Set(claim.Token, token)
				c.Set(claim.UserId, userId)
				c.Set(claim.Username, introspect.UserName)
				var roles []string
				var permissions []string
				for _, role := range introspect.Roles {
					if role[:2] == permission.Prefix {
						permissions = append(permissions, role)
					} else {
						roles = append(roles, role)
					}
				}
				c.Set(claim.IsLogin, true)
				c.Set(claim.Roles, roles)
				c.Set(claim.Permissions, permissions)
				c.Set(claim.Currency, "USD")
				c.Set("areaUnit", "sqrm")
				// TODO: 將新舊 official 帳號同步後，此段就可拿掉
				if strings.ToLower(c.GetString(claim.Org)) == "wota" && c.GetHeader("User-Id") != "" {
					c.Set(claim.TraceId, fmt.Sprintf("%s_%s", c.GetString(claim.Org), c.GetHeader("User-Id")))
				} else {
					c.Set(claim.TraceId, fmt.Sprintf("%s_%d", c.GetString(claim.Org), userId))
				}
				c.Set(claim.FromApi, isFromAPI)
				c.Set(claim.IsApiTestMode, isApiTestMode)

				//fmt.Println("username:", c.GetString(claim.Username))
				//fmt.Println("role:", c.GetStringSlice(claim.Roles))
				//fmt.Println("permission:", c.GetStringSlice(claim.Permissions))

				c.Next()
			}
		} else if c.GetString(claim.Org) == config.MainOrg {
			// Wota 免登入模式，for official 未登入的時機
			c.Set(claim.IsGuest, true)
			c.Set(claim.Roles, []string{role.Guest})
			c.Set(claim.TraceId, "wota_guest")
		}
		c.Next()
	}
}
