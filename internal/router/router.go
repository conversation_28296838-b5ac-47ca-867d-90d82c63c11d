package router

import (
	"bitbucket.org/actechinc/wota_ship_crawler/internal/controller"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/middleware"
	"github.com/gin-gonic/gin"
)

func SetupRouter() (*gin.Engine, error) {
	router := gin.Default()
	if err := router.SetTrustedProxies(nil); err != nil {
		return nil, err
	}

	// NOTE: 子 router 沒有包在 router.Group 內，就不會經過該層的 middleware
	router.Use(middleware.CORS())
	router.Use(middleware.Language())
	router.Use(middleware.Platform())
	router.Use(middleware.ParseToken())

	v1 := router.Group("/v1")
	{
		controller.InitHealthAPI(v1)
		controller.InitRegentSevenSeasCrawlAPI(v1)
		controller.InitSilverSeasCrawlAPI(v1)
	}

	return router, nil
}
