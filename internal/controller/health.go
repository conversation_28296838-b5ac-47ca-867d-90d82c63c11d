package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

func InitHealthAPI(rg *gin.RouterGroup) {
	rg.GET("/ping", getPingPong)
	rg.GET("/version", getVersion)
	rg.GET("/secret", checkSecret)
}

// @Summary PingPong
// @Tags Health
// @Success 200 {string} json
// @Router /ping [get]
func getPingPong(c *gin.Context) {
	c.IndentedJSON(http.StatusOK, "pong")
}

// @Summary 取得版本相關資訊
// @Tags Health
// @Success 200 {string} json
// @Router /version [get]
func getVersion(c *gin.Context) {
	c.IndentedJSON(http.StatusOK, map[string]interface{}{"Build": config.BuildVersion, "Commit": config.CommitId})
}

// @Summary 檢查 secret 是否有在使用
// @Tags Health
// @Success 200 {string} json
// @Router /secret [get]
func checkSecret(c *gin.Context) {
	c.IndentedJSON(http.StatusOK, config.SsmInUse)
}
