package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/status"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto/common"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/model"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service/silver_seas"
	"github.com/gin-gonic/gin"
)

func InitSilverSeasCrawlAPI(rg *gin.RouterGroup) {
	rgAuth := rg.Group("crawler/silver-seas")
	{
		rgAuth.POST("specs", crawlSilverSeasSpecs)
		rgAuth.POST("deck-images", crawlSilverSeasDeckImages)
		rgAuth.POST("cover-media", crawlSilverSeasCoverMedia)
		rgAuth.POST("suites", crawlSilverSeasSuites)
		rgAuth.POST("cruises", crawlSilverSeasCruises)
		rgAuth.POST("activities", crawlSilverSeasActivities)
	}
}

var silverSeasShipUrlMap = map[string]string{
	// Ocean ships (8 ships)
	"dawn":    "https://www.silversea.com/ships/silver-dawn.html",
	"moon":    "https://www.silversea.com/ships/silver-moon.html",
	"muse":    "https://www.silversea.com/ships/silver-muse.html",
	"nova":    "https://www.silversea.com/ships/silver-nova.html",
	"ray":     "https://www.silversea.com/ships/silver-ray.html",
	"shadow":  "https://www.silversea.com/ships/silver-shadow.html",
	"spirit":  "https://www.silversea.com/ships/silver-spirit.html",
	"whisper": "https://www.silversea.com/ships/silver-whisper.html",
	// Expedition ships (4 ships)
	"cloud":     "https://www.silversea.com/ships/silver-cloud-expedition.html",
	"endeavour": "https://www.silversea.com/ships/silver-endeavour.html",
	"origin":    "https://www.silversea.com/ships/silver-origin.html",
	"wind":      "https://www.silversea.com/ships/silver-wind.html",
}

// @Summary  爬取 Silver Seas 船隻規格資料
// @Description 爬取 Silver Seas 船隻規格資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlSilverSeasSpecReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/silver-seas/specs [POST]
func crawlSilverSeasSpecs(c *gin.Context) {
	var req dto.PostCrawlSilverSeasSpecReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		startTime := time.Now()
		task := &model.CrawlerShipTask{
			Type:         "manual",
			Method:       "crawlSilverSeasSpecs",
			Body:         fmt.Sprintf(`{"callbackUrl":"%s"}`, callbackUrl),
			ResponseJson: `{}`,
			StartTime:    &startTime,
			Status:       "running",
		}
		err = model.SaveOrUpdate(task)
		if err != nil {
			logger.Error().Err(err).Msg("Error saving task")
			return
		}

		var results []dto.ShipSpec

		for shipUrlName, shipUrl := range silverSeasShipUrlMap {
			crawler := silver_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			spec, err := crawler.CrawlShipSpec(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrl", shipUrl).Msg("Error crawling ship spec")

				callbackData := struct {
					Success bool           `json:"success"`
					Error   string         `json:"error"`
					Results []dto.ShipSpec `json:"results"`
				}{
					Success: false,
					Error:   fmt.Sprintf("Failed to crawl spec for %s: %v", shipUrlName, err),
					Results: results,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					return
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				return
			}
			results = append(results, spec)
			logger.Info().Str("shipUrlName", shipUrlName).Msg("Successfully crawled ship spec")
		}

		callbackData := struct {
			Success bool           `json:"success"`
			Error   string         `json:"error,omitempty"`
			Results []dto.ShipSpec `json:"results"`
		}{
			Success: true,
			Results: results,
		}

		jsonData, err := json.Marshal(callbackData)
		if err != nil {
			logger.Error().Err(err).Msg("Error marshaling callback data")
			return
		}

		endTime := time.Now()
		task.EndTime = &endTime
		task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
		task.ResponseJson = string(jsonData)
		task.Status = "completed"
		if err := model.SaveOrUpdate(task); err != nil {
			logger.Error().Err(err).Msg("Error updating task")
		}

		resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
			return
		}
		defer resp.Body.Close()
	}(req.CallbackUrl)
}

// @Summary  爬取 Silver Seas 船隻甲板圖片資料
// @Description 爬取 Silver Seas 船隻甲板圖片資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlSilverSeasDeckImageReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/silver-seas/deck-images [POST]
func crawlSilverSeasDeckImages(c *gin.Context) {
	var req dto.PostCrawlSilverSeasDeckImageReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		startTime := time.Now()
		task := &model.CrawlerShipTask{
			Type:         "manual",
			Method:       "crawlSilverSeasDeckImages",
			Body:         fmt.Sprintf(`{"callbackUrl":"%s"}`, callbackUrl),
			ResponseJson: `{}`,
			StartTime:    &startTime,
			Status:       "running",
		}
		err = model.SaveOrUpdate(task)
		if err != nil {
			logger.Error().Err(err).Msg("Error saving task")
			return
		}

		var results []dto.ShipDeckImage
		for shipUrlName, shipUrl := range silverSeasShipUrlMap {
			logger.Info().Str("shipUrlName", shipUrlName).Str("shipUrl", shipUrl).Msg("Crawling deck image")
			crawler := silver_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			deckImage, err := crawler.CrawlShipDeckImages(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrlName", shipUrlName).Msg("Error crawling deck image")

				callbackData := struct {
					Success bool                `json:"success"`
					Error   string              `json:"error"`
					Results []dto.ShipDeckImage `json:"results"`
				}{
					Success: false,
					Error:   fmt.Sprintf("Failed to crawl deck images for %s: %v", shipUrlName, err),
					Results: results,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					return
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				return
			}
			results = append(results, deckImage)

			logger.Info().Msgf("Successfully crawled deck image for %s", shipUrlName)
		}

		callbackData := struct {
			Success bool                `json:"success"`
			Error   string              `json:"error,omitempty"`
			Results []dto.ShipDeckImage `json:"results"`
		}{
			Success: true,
			Results: results,
		}

		jsonData, err := json.Marshal(callbackData)
		if err != nil {
			logger.Error().Err(err).Msg("Error marshaling callback data")
			return
		}

		endTime := time.Now()
		task.EndTime = &endTime
		task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
		task.ResponseJson = string(jsonData)
		task.Status = "completed"
		if err := model.SaveOrUpdate(task); err != nil {
			logger.Error().Err(err).Msg("Error updating task")
		}

		resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
			return
		}
		defer resp.Body.Close()
	}(req.CallbackUrl)
}

// @Summary  爬取 Silver Seas 船隻封面媒體資料
// @Description 爬取 Silver Seas 船隻封面媒體資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlSilverSeasCoverMediaReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/silver-seas/cover-media [POST]
func crawlSilverSeasCoverMedia(c *gin.Context) {
	var req dto.PostCrawlSilverSeasCoverMediaReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		startTime := time.Now()
		task := &model.CrawlerShipTask{
			Type:         "manual",
			Method:       "crawlSilverSeasCoverMedia",
			Body:         fmt.Sprintf(`{"callbackUrl":"%s"}`, callbackUrl),
			ResponseJson: `{}`,
			StartTime:    &startTime,
			Status:       "running",
		}
		err = model.SaveOrUpdate(task)
		if err != nil {
			logger.Error().Err(err).Msg("Error saving task")
			return
		}

		var results []dto.ShipCoverMedia

		for shipUrlName, shipUrl := range silverSeasShipUrlMap {
			crawler := silver_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			coverMedia, err := crawler.CrawlShipCoverMedia(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrlName", shipUrlName).Msg("Error crawling ship cover media")

				callbackData := struct {
					Success bool                 `json:"success"`
					Error   string               `json:"error"`
					Results []dto.ShipCoverMedia `json:"results"`
				}{
					Success: false,
					Error:   fmt.Sprintf("Failed to crawl cover media for %s: %v", shipUrlName, err),
					Results: results,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					return
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				return
			}
			results = append(results, coverMedia)
			logger.Info().Msgf("Successfully crawled ship cover media for %s", shipUrlName)
		}

		callbackData := struct {
			Success bool                 `json:"success"`
			Error   string               `json:"error,omitempty"`
			Results []dto.ShipCoverMedia `json:"results"`
		}{
			Success: true,
			Results: results,
		}

		jsonData, err := json.Marshal(callbackData)
		if err != nil {
			logger.Error().Err(err).Msg("Error marshaling callback data")
			return
		}

		endTime := time.Now()
		task.EndTime = &endTime
		task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
		task.ResponseJson = string(jsonData)
		task.Status = "completed"
		if err := model.SaveOrUpdate(task); err != nil {
			logger.Error().Err(err).Msg("Error updating task")
		}

		resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
			return
		}
		defer resp.Body.Close()
	}(req.CallbackUrl)
}

// @Summary  爬取 Silver Seas 船隻套房資料
// @Description 爬取 Silver Seas 船隻套房資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlSilverSeasSuitesReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/silver-seas/suites [POST]
func crawlSilverSeasSuites(c *gin.Context) {
	var req dto.PostCrawlSilverSeasSuitesReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		startTime := time.Now()
		task := &model.CrawlerShipTask{
			Type:         "manual",
			Method:       "crawlSilverSeasSuites",
			Body:         fmt.Sprintf(`{"callbackUrl":"%s"}`, callbackUrl),
			ResponseJson: `{}`,
			StartTime:    &startTime,
			Status:       "running",
		}
		err = model.SaveOrUpdate(task)
		if err != nil {
			logger.Error().Err(err).Msg("Error saving task")
			return
		}

		var results []dto.ShipSuite

		for shipUrlName, shipUrl := range silverSeasShipUrlMap {
			crawler := silver_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			shipSuites, err := crawler.CrawlShipSuites(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrlName", shipUrlName).Msg("Error crawling ship suites")

				callbackData := struct {
					Success bool            `json:"success"`
					Error   string          `json:"error"`
					Results []dto.ShipSuite `json:"results"`
				}{
					Success: false,
					Error:   fmt.Sprintf("Failed to crawl suites for %s: %v", shipUrlName, err),
					Results: results,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					return
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				return
			}
			results = append(results, shipSuites...)
			logger.Info().Msgf("Successfully crawled ship suites for %s", shipUrlName)
		}

		callbackData := struct {
			Success bool            `json:"success"`
			Error   string          `json:"error,omitempty"`
			Results []dto.ShipSuite `json:"results"`
		}{
			Success: true,
			Results: results,
		}

		jsonData, err := json.Marshal(callbackData)
		if err != nil {
			logger.Error().Err(err).Msg("Error marshaling callback data")
			return
		}

		endTime := time.Now()
		task.EndTime = &endTime
		task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
		task.ResponseJson = string(jsonData)
		task.Status = "completed"
		if err := model.SaveOrUpdate(task); err != nil {
			logger.Error().Err(err).Msg("Error updating task")
		}

		resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
			return
		}
		defer resp.Body.Close()
	}(req.CallbackUrl)
}

// @Summary  爬取 Silver Seas 船隻航線資料
// @Description 爬取 Silver Seas 船隻航線資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlSilverSeasCruisesReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/silver-seas/cruises [POST]
func crawlSilverSeasCruises(c *gin.Context) {
	var req dto.PostCrawlSilverSeasCruisesReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		for shipUrlName, shipUrl := range silverSeasShipUrlMap {
			startTime := time.Now()
			task := &model.CrawlerShipTask{
				Type:         "manual",
				Method:       "crawlSilverSeasCruises",
				Body:         fmt.Sprintf(`{"callbackUrl":"%s","shipUrlName":"%s","shipUrl":"%s"}`, callbackUrl, shipUrlName, shipUrl),
				ResponseJson: `{}`,
				StartTime:    &startTime,
				Status:       "running",
			}
			if err := model.SaveOrUpdate(task); err != nil {
				logger.Error().Err(err).Msg("Error saving task")
				continue
			}

			crawler := silver_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			shipCruises, err := crawler.CrawlShipCruises(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrlName", shipUrlName).Msg("Error crawling cruises")

				callbackData := struct {
					Success     bool   `json:"success"`
					Error       string `json:"error"`
					ShipUrlName string `json:"shipUrlName"`
					ShipUrl     string `json:"shipUrl"`
				}{
					Success:     false,
					Error:       err.Error(),
					ShipUrlName: shipUrlName,
					ShipUrl:     shipUrl,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					continue
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				continue
			}

			for i := range shipCruises {
				if shipCruises[i].DetailLink != "" {
					logger.Info().Str("detailLink", shipCruises[i].DetailLink).Msg("Crawling itinerary")
					itineraries, mapViewS3Path, err := crawler.CrawlShipItineraries(shipCruises[i].DetailLink, shipCruises[i].From, shipCruises[i].To)
					if err != nil {
						logger.Error().Err(err).Str("detailLink", shipCruises[i].DetailLink).Msg("Error crawling itinerary")

						callbackData := struct {
							Success     bool   `json:"success"`
							Error       string `json:"error"`
							ShipUrlName string `json:"shipUrlName"`
							ShipUrl     string `json:"shipUrl"`
						}{
							Success:     false,
							Error:       fmt.Sprintf("Failed to crawl itinerary for %s: %v", shipUrlName, err),
							ShipUrlName: shipUrlName,
							ShipUrl:     shipUrl,
						}

						jsonData, jsonErr := json.Marshal(callbackData)
						if jsonErr != nil {
							logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
							continue
						}
						endTime := time.Now()
						task.EndTime = &endTime
						task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
						task.ResponseJson = string(jsonData)
						task.Status = "failed"
						task.ErrorMsg = err.Error()
						if err := model.SaveOrUpdate(task); err != nil {
							logger.Error().Err(err).Msg("Error updating task")
						}

						if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
							logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
						}
						continue
					}
					shipCruises[i].Itinerary = itineraries
					shipCruises[i].MapViewS3Path = mapViewS3Path
					logger.Info().Str("detailLink", shipCruises[i].DetailLink).Msg("Successfully crawled itinerary")
				}
			}

			logger.Info().Int("cruiseCount", len(shipCruises)).Msgf("Successfully crawled cruises for %s", shipUrlName)

			callbackData := struct {
				Success     bool             `json:"success"`
				ShipUrlName string           `json:"shipUrlName"`
				ShipUrl     string           `json:"shipUrl"`
				Results     []dto.ShipCruise `json:"results"`
			}{
				Success:     true,
				ShipUrlName: shipUrlName,
				ShipUrl:     shipUrl,
				Results:     shipCruises,
			}

			jsonData, err := json.Marshal(callbackData)
			if err != nil {
				logger.Error().Err(err).Msg("Error marshaling callback data")
				continue
			}

			endTime := time.Now()
			task.EndTime = &endTime
			task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
			task.ResponseJson = string(jsonData)
			task.Status = "completed"
			if err := model.SaveOrUpdate(task); err != nil {
				logger.Error().Err(err).Msg("Error updating task")
			}

			resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
			if err != nil {
				logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				continue
			}
			defer resp.Body.Close()
		}
	}(req.CallbackUrl)
}

// @Summary  爬取 Silver Seas 船隻活動資料
// @Description 爬取 Silver Seas 船隻活動資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlSilverSeasActivityReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/silver-seas/activities [POST]
func crawlSilverSeasActivities(c *gin.Context) {
	var req dto.PostCrawlSilverSeasActivityReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		startTime := time.Now()
		task := &model.CrawlerShipTask{
			Type:         "manual",
			Method:       "crawlSilverSeasActivities",
			Body:         fmt.Sprintf(`{"callbackUrl":"%s"}`, callbackUrl),
			ResponseJson: `{}`,
			StartTime:    &startTime,
			Status:       "running",
		}
		err = model.SaveOrUpdate(task)
		if err != nil {
			logger.Error().Err(err).Msg("Error saving task")
			return
		}

		var results []dto.ShipActivity
		for shipUrlName, shipUrl := range silverSeasShipUrlMap {
			logger.Info().Str("shipUrlName", shipUrlName).Str("shipUrl", shipUrl).Msg("Crawling activities")
			crawler := silver_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			shipActivities, err := crawler.CrawlShipActivity(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrlName", shipUrlName).Msg("Error crawling ship activities")

				callbackData := struct {
					Success bool               `json:"success"`
					Error   string             `json:"error"`
					Results []dto.ShipActivity `json:"results"`
				}{
					Success: false,
					Error:   fmt.Sprintf("Failed to crawl activities for %s: %v", shipUrlName, err),
					Results: results,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					return
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				return
			}

			results = append(results, shipActivities...)

			logger.Info().Int("activityCount", len(shipActivities)).Msgf("Successfully crawled activities for %s", shipUrlName)
		}

		callbackData := struct {
			Success bool               `json:"success"`
			Error   string             `json:"error,omitempty"`
			Results []dto.ShipActivity `json:"results"`
		}{
			Success: true,
			Results: results,
		}

		jsonData, err := json.Marshal(callbackData)
		if err != nil {
			logger.Error().Err(err).Msg("Error marshaling callback data")
			return
		}

		endTime := time.Now()
		task.EndTime = &endTime
		task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
		task.ResponseJson = string(jsonData)
		task.Status = "completed"
		if err := model.SaveOrUpdate(task); err != nil {
			logger.Error().Err(err).Msg("Error updating task")
		}

		resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
			return
		}
		defer resp.Body.Close()
	}(req.CallbackUrl)
}
