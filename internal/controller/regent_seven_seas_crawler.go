package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/ship"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/status"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/dto/common"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/model"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/service/regent_seven_seas"
	"github.com/gin-gonic/gin"
)

func InitRegentSevenSeasCrawlAPI(rg *gin.RouterGroup) {
	rgAuth := rg.Group("crawler/regent-seven-seas")
	{
		rgAuth.POST("specs", crawlRegentSevenSeasSpecs)
		rgAuth.POST("deck-images", crawlRegentSevenSeasDeckImages)
		rgAuth.POST("cover-media", crawlRegentSevenSeasCoverMedia)
		rgAuth.POST("videos", crawlRegentSevenSeasVideos)
		rgAuth.POST("suites", crawlRegentSevenSeasSuites)
		rgAuth.POST("cruises", crawlRegentSevenSeasCruises)
		rgAuth.POST("activities", crawlRegentSevenSeasActivities)
	}
}

var shipUrlMap = map[string]string{
	"grandeur":  "https://www.rssc.com/ships/seven_seas_grandeur",
	"splendor":  "https://www.rssc.com/ships/seven_seas_splendor",
	"explorer":  "https://www.rssc.com/ships/seven_seas_explorer",
	"voyager":   "https://www.rssc.com/ships/seven_seas_voyager",
	"mariner":   "https://www.rssc.com/ships/seven_seas_mariner",
	"navigator": "https://www.rssc.com/ships/seven_seas_navigator",
}

// @Summary  爬取 Regent Seven Seas 船隻規格資料
// @Description 爬取 Regent Seven Seas 船隻規格資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlRegentSevenSeasSpecReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/regent-seven-seas/specs [POST]
func crawlRegentSevenSeasSpecs(c *gin.Context) {
	var req dto.PostCrawlRegentSevenSeasSpecReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		startTime := time.Now()
		task := &model.CrawlerShipTask{
			Type:         "manual",
			Method:       "crawlRegentSevenSeasSpecs",
			Body:         fmt.Sprintf(`{"callbackUrl":"%s"}`, callbackUrl),
			ResponseJson: `{}`,
			StartTime:    &startTime,
			Status:       "running",
		}
		err = model.SaveOrUpdate(task)
		if err != nil {
			logger.Error().Err(err).Msg("Error saving task")
			return
		}

		var results []dto.ShipSpec

		for shipUrlName, shipUrl := range shipUrlMap {
			crawler := regent_seven_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			spec, err := crawler.CrawlShipSpec(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrl", shipUrl).Msg("Error crawling ship spec")

				callbackData := struct {
					Success bool           `json:"success"`
					Error   string         `json:"error"`
					Results []dto.ShipSpec `json:"results"`
				}{
					Success: false,
					Error:   fmt.Sprintf("Failed to crawl spec for %s: %v", shipUrlName, err),
					Results: results,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					return
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				return
			}
			results = append(results, spec)
			logger.Info().Str("shipUrlName", shipUrlName).Msg("Successfully crawled ship spec")
		}

		callbackData := struct {
			Success bool           `json:"success"`
			Error   string         `json:"error,omitempty"`
			Results []dto.ShipSpec `json:"results"`
		}{
			Success: true,
			Results: results,
		}

		jsonData, err := json.Marshal(callbackData)
		if err != nil {
			logger.Error().Err(err).Msg("Error marshaling callback data")
			return
		}

		endTime := time.Now()
		task.EndTime = &endTime
		task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
		task.ResponseJson = string(jsonData)
		task.Status = "completed"
		if err := model.SaveOrUpdate(task); err != nil {
			logger.Error().Err(err).Msg("Error updating task")
		}

		resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
			return
		}
		defer resp.Body.Close()
	}(req.CallbackUrl)
}

// @Summary  爬取 Regent Seven Seas 船隻甲板圖片資料
// @Description 爬取 Regent Seven Seas 船隻甲板圖片資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlRegentSevenSeasDeckImageReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/regent-seven-seas/deck-images [POST]
func crawlRegentSevenSeasDeckImages(c *gin.Context) {
	var req dto.PostCrawlRegentSevenSeasDeckImageReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		startTime := time.Now()
		task := &model.CrawlerShipTask{
			Type:         "manual",
			Method:       "crawlRegentSevenSeasDeckImages",
			Body:         fmt.Sprintf(`{"callbackUrl":"%s"}`, callbackUrl),
			ResponseJson: `{}`,
			StartTime:    &startTime,
			Status:       "running",
		}
		err = model.SaveOrUpdate(task)
		if err != nil {
			logger.Error().Err(err).Msg("Error saving task")
			return
		}

		var results []dto.ShipDeckImage

		shipNameShortMap := map[string]string{
			"grandeur":  ship.SevenSeasGrandeurShort,
			"splendor":  ship.SevenSeasSplendorShort,
			"explorer":  ship.SevenSeasExplorerShort,
			"voyager":   ship.SevenSeasVoyagerShort,
			"mariner":   ship.SevenSeasMarinerShort,
			"navigator": ship.SevenSeasNavigatorShort,
		}

		for shipUrlName := range shipUrlMap {
			deckImageUrl := fmt.Sprintf("https://www.rssc.com/ships/explorer?ship=%s", shipNameShortMap[shipUrlName])

			logger.Info().Str("shipUrlName", shipUrlName).Str("deckImageUrl", deckImageUrl).Msg("Crawling deck image")
			crawler := regent_seven_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			deckImage, err := crawler.CrawlShipDeckImages(deckImageUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrlName", shipUrlName).Msg("Error crawling deck image")

				callbackData := struct {
					Success bool                `json:"success"`
					Error   string              `json:"error"`
					Results []dto.ShipDeckImage `json:"results"`
				}{
					Success: false,
					Error:   fmt.Sprintf("Failed to crawl deck images for %s: %v", shipUrlName, err),
					Results: results,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					return
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				return
			}
			results = append(results, deckImage)
			logger.Info().Msgf("Successfully crawled deck image for %s", shipUrlName)
		}

		callbackData := struct {
			Success bool                `json:"success"`
			Error   string              `json:"error,omitempty"`
			Results []dto.ShipDeckImage `json:"results"`
		}{
			Success: true,
			Results: results,
		}

		jsonData, err := json.Marshal(callbackData)
		if err != nil {
			logger.Error().Err(err).Msg("Error marshaling callback data")
			return
		}

		endTime := time.Now()
		task.EndTime = &endTime
		task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
		task.ResponseJson = string(jsonData)
		task.Status = "completed"
		if err := model.SaveOrUpdate(task); err != nil {
			logger.Error().Err(err).Msg("Error updating task")
		}

		resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
			return
		}
		defer resp.Body.Close()
	}(req.CallbackUrl)
}

// @Summary  爬取 Regent Seven Seas 船隻封面媒體資料
// @Description 爬取 Regent Seven Seas 船隻封面媒體資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlRegentSevenSeasCoverMediaReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/regent-seven-seas/cover-media [POST]
func crawlRegentSevenSeasCoverMedia(c *gin.Context) {
	var req dto.PostCrawlRegentSevenSeasCoverMediaReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		startTime := time.Now()
		task := &model.CrawlerShipTask{
			Type:         "manual",
			Method:       "crawlRegentSevenSeasCoverMedia",
			Body:         fmt.Sprintf(`{"callbackUrl":"%s"}`, callbackUrl),
			ResponseJson: `{}`,
			StartTime:    &startTime,
			Status:       "running",
		}
		err = model.SaveOrUpdate(task)
		if err != nil {
			logger.Error().Err(err).Msg("Error saving task")
			return
		}

		var results []dto.ShipCoverMedia

		for shipUrlName, shipUrl := range shipUrlMap {
			crawler := regent_seven_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			coverMedia, err := crawler.CrawlShipCoverMedia(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrlName", shipUrlName).Msg("Error crawling ship cover media")

				callbackData := struct {
					Success bool                 `json:"success"`
					Error   string               `json:"error"`
					Results []dto.ShipCoverMedia `json:"results"`
				}{
					Success: false,
					Error:   fmt.Sprintf("Failed to crawl cover media for %s: %v", shipUrlName, err),
					Results: results,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					return
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				return
			}
			results = append(results, coverMedia)
			logger.Info().Msgf("Successfully crawled ship cover media for %s", shipUrlName)
		}

		callbackData := struct {
			Success bool                 `json:"success"`
			Error   string               `json:"error,omitempty"`
			Results []dto.ShipCoverMedia `json:"results"`
		}{
			Success: true,
			Results: results,
		}

		jsonData, err := json.Marshal(callbackData)
		if err != nil {
			logger.Error().Err(err).Msg("Error marshaling callback data")
			return
		}

		endTime := time.Now()
		task.EndTime = &endTime
		task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
		task.ResponseJson = string(jsonData)
		task.Status = "completed"
		if err := model.SaveOrUpdate(task); err != nil {
			logger.Error().Err(err).Msg("Error updating task")
		}

		resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
			return
		}
		defer resp.Body.Close()
	}(req.CallbackUrl)
}

// @Summary  爬取 Regent Seven Seas 船隻影片資料
// @Description 爬取 Regent Seven Seas 船隻影片資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlRegentSevenSeasVideoReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/regent-seven-seas/videos [POST]
func crawlRegentSevenSeasVideos(c *gin.Context) {
	var req dto.PostCrawlRegentSevenSeasVideoReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		startTime := time.Now()
		task := &model.CrawlerShipTask{
			Type:         "manual",
			Method:       "crawlRegentSevenSeasVideos",
			Body:         fmt.Sprintf(`{"callbackUrl":"%s"}`, callbackUrl),
			ResponseJson: `{}`,
			StartTime:    &startTime,
			Status:       "running",
		}
		err = model.SaveOrUpdate(task)
		if err != nil {
			logger.Error().Err(err).Msg("Error saving task")
			return
		}

		var results []dto.ShipVideo

		for shipUrlName, shipUrl := range shipUrlMap {
			crawler := regent_seven_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			video, err := crawler.CrawlShipVideo(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrlName", shipUrlName).Msg("Error crawling ship video")

				callbackData := struct {
					Success bool            `json:"success"`
					Error   string          `json:"error"`
					Results []dto.ShipVideo `json:"results"`
				}{
					Success: false,
					Error:   fmt.Sprintf("Failed to crawl video for %s: %v", shipUrlName, err),
					Results: results,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					return
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				return
			}
			results = append(results, video)
			logger.Info().Msgf("Successfully crawled ship video for %s", shipUrlName)
		}

		callbackData := struct {
			Success bool            `json:"success"`
			Error   string          `json:"error,omitempty"`
			Results []dto.ShipVideo `json:"results"`
		}{
			Success: true,
			Results: results,
		}

		jsonData, err := json.Marshal(callbackData)
		if err != nil {
			logger.Error().Err(err).Msg("Error marshaling callback data")
			return
		}

		endTime := time.Now()
		task.EndTime = &endTime
		task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
		task.ResponseJson = string(jsonData)
		task.Status = "completed"
		if err := model.SaveOrUpdate(task); err != nil {
			logger.Error().Err(err).Msg("Error updating task")
		}

		resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
			return
		}
		defer resp.Body.Close()
	}(req.CallbackUrl)
}

// @Summary  爬取 Regent Seven Seas 船隻套房資料
// @Description 爬取 Regent Seven Seas 船隻套房資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlRegentSevenSeasSuitesReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/regent-seven-seas/suites [POST]
func crawlRegentSevenSeasSuites(c *gin.Context) {
	var req dto.PostCrawlRegentSevenSeasSuitesReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		startTime := time.Now()
		task := &model.CrawlerShipTask{
			Type:         "manual",
			Method:       "crawlRegentSevenSeasSuites",
			Body:         fmt.Sprintf(`{"callbackUrl":"%s"}`, callbackUrl),
			ResponseJson: `{}`,
			StartTime:    &startTime,
			Status:       "running",
		}
		err = model.SaveOrUpdate(task)
		if err != nil {
			logger.Error().Err(err).Msg("Error saving task")
			return
		}

		var results []dto.ShipSuite

		for shipUrlName, shipUrl := range shipUrlMap {
			crawler := regent_seven_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			shipSuites, err := crawler.CrawlShipSuites(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrlName", shipUrlName).Msg("Error crawling ship suites")

				callbackData := struct {
					Success bool            `json:"success"`
					Error   string          `json:"error"`
					Results []dto.ShipSuite `json:"results"`
				}{
					Success: false,
					Error:   fmt.Sprintf("Failed to crawl suites for %s: %v", shipUrlName, err),
					Results: results,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					return
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				return
			}
			results = append(results, shipSuites...)
			logger.Info().Msgf("Successfully crawled ship suites for %s", shipUrlName)
		}

		callbackData := struct {
			Success bool            `json:"success"`
			Error   string          `json:"error,omitempty"`
			Results []dto.ShipSuite `json:"results"`
		}{
			Success: true,
			Results: results,
		}

		jsonData, err := json.Marshal(callbackData)
		if err != nil {
			logger.Error().Err(err).Msg("Error marshaling callback data")
			return
		}

		endTime := time.Now()
		task.EndTime = &endTime
		task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
		task.ResponseJson = string(jsonData)
		task.Status = "completed"
		if err := model.SaveOrUpdate(task); err != nil {
			logger.Error().Err(err).Msg("Error updating task")
		}

		resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
			return
		}
		defer resp.Body.Close()
	}(req.CallbackUrl)
}

// @Summary  爬取 Regent Seven Seas 船隻航線資料
// @Description 爬取 Regent Seven Seas 船隻航線資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlRegentSevenSeasCruisesReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/regent-seven-seas/cruises [POST]
func crawlRegentSevenSeasCruises(c *gin.Context) {
	var req dto.PostCrawlRegentSevenSeasCruisesReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		for shipUrlName, shipUrl := range shipUrlMap {
			startTime := time.Now()
			task := &model.CrawlerShipTask{
				Type:         "manual",
				Method:       "crawlRegentSevenSeasCruises",
				Body:         fmt.Sprintf(`{"callbackUrl":"%s","shipUrlName":"%s","shipUrl":"%s"}`, callbackUrl, shipUrlName, shipUrl),
				ResponseJson: `{}`,
				StartTime:    &startTime,
				Status:       "running",
			}
			if err := model.SaveOrUpdate(task); err != nil {
				logger.Error().Err(err).Msg("Error saving task")
				continue
			}

			crawler := regent_seven_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			shipCruises, err := crawler.CrawlShipCruises(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrlName", shipUrlName).Msg("Error crawling cruises")

				callbackData := struct {
					Success     bool   `json:"success"`
					Error       string `json:"error"`
					ShipUrlName string `json:"shipUrlName"`
					ShipUrl     string `json:"shipUrl"`
				}{
					Success:     false,
					Error:       err.Error(),
					ShipUrlName: shipUrlName,
					ShipUrl:     shipUrl,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					continue
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				continue
			}

			for i := range shipCruises {
				if shipCruises[i].DetailLink != "" {
					logger.Info().Str("detailLink", shipCruises[i].DetailLink).Msg("Crawling itinerary")
					itinerary, isWaitlisted, price, err := crawler.CrawlShipItineraries(shipCruises[i].DetailLink, shipCruises[i].From, shipCruises[i].To)
					if err != nil {
						logger.Error().Err(err).Str("detailLink", shipCruises[i].DetailLink).Msg("Error crawling itinerary")

						callbackData := struct {
							Success     bool   `json:"success"`
							Error       string `json:"error"`
							ShipUrlName string `json:"shipUrlName"`
							ShipUrl     string `json:"shipUrl"`
						}{
							Success:     false,
							Error:       fmt.Sprintf("Failed to crawl itinerary for %s: %v", shipUrlName, err),
							ShipUrlName: shipUrlName,
							ShipUrl:     shipUrl,
						}

						jsonData, jsonErr := json.Marshal(callbackData)
						if jsonErr != nil {
							logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
							continue
						}
						endTime := time.Now()
						task.EndTime = &endTime
						task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
						task.ResponseJson = string(jsonData)
						task.Status = "failed"
						task.ErrorMsg = err.Error()
						if err := model.SaveOrUpdate(task); err != nil {
							logger.Error().Err(err).Msg("Error updating task")
						}

						if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
							logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
						}
						continue
					}
					shipCruises[i].Itinerary = itinerary
					shipCruises[i].IsWaitlisted = isWaitlisted
					shipCruises[i].Price = price
					logger.Info().Str("detailLink", shipCruises[i].DetailLink).Msg("Successfully crawled itinerary")
				}
			}

			for i := range shipCruises {
				if shipCruises[i].DetailLink != "" {
					logger.Info().Str("detailLink", shipCruises[i].DetailLink).Msg("Crawling map view")
					mapViewS3Path, err := crawler.CrawlShipItineraryMapView(shipCruises[i].DetailLink)
					if err != nil {
						logger.Error().Err(err).Str("detailLink", shipCruises[i].DetailLink).Msg("Error crawling map view")

						callbackData := struct {
							Success     bool   `json:"success"`
							Error       string `json:"error"`
							ShipUrlName string `json:"shipUrlName"`
							ShipUrl     string `json:"shipUrl"`
						}{
							Success:     false,
							Error:       fmt.Sprintf("Failed to crawl map view for %s: %v", shipUrlName, err),
							ShipUrlName: shipUrlName,
							ShipUrl:     shipUrl,
						}

						jsonData, jsonErr := json.Marshal(callbackData)
						if jsonErr != nil {
							logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
							continue
						}
						endTime := time.Now()
						task.EndTime = &endTime
						task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
						task.ResponseJson = string(jsonData)
						task.Status = "failed"
						task.ErrorMsg = err.Error()
						if err := model.SaveOrUpdate(task); err != nil {
							logger.Error().Err(err).Msg("Error updating task")
						}

						if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
							logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
						}
						continue
					}
					shipCruises[i].MapViewS3Path = mapViewS3Path
					logger.Info().Str("detailLink", shipCruises[i].DetailLink).Msg("Successfully crawled map view")
				}
			}

			logger.Info().Int("cruiseCount", len(shipCruises)).Msgf("Successfully crawled cruises for %s", shipUrlName)

			callbackData := struct {
				Success     bool             `json:"success"`
				ShipUrlName string           `json:"shipUrlName"`
				ShipUrl     string           `json:"shipUrl"`
				Results     []dto.ShipCruise `json:"results"`
			}{
				Success:     true,
				ShipUrlName: shipUrlName,
				ShipUrl:     shipUrl,
				Results:     shipCruises,
			}

			jsonData, err := json.Marshal(callbackData)
			if err != nil {
				logger.Error().Err(err).Msg("Error marshaling callback data")
				continue
			}

			endTime := time.Now()
			task.EndTime = &endTime
			task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
			task.ResponseJson = string(jsonData)
			task.Status = "completed"
			if err := model.SaveOrUpdate(task); err != nil {
				logger.Error().Err(err).Msg("Error updating task")
			}

			resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
			if err != nil {
				logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				continue
			}
			defer resp.Body.Close()
		}
	}(req.CallbackUrl)
}

// @Summary  爬取 Regent Seven Seas 船隻活動資料
// @Description 爬取 Regent Seven Seas 船隻活動資料
// @Tags     Crawler
// @Security ApiKeyAuth
// @produce  json
// @Param    Body body dto.PostCrawlRegentSevenSeasActivityReq true "callbackUrl"
// @Success  200 {object} common.GeneralResponse
// @Failure  400 {object} common.GeneralFailedResponse
// @Router   /crawler/regent-seven-seas/activities [POST]
func crawlRegentSevenSeasActivities(c *gin.Context) {
	var req dto.PostCrawlRegentSevenSeasActivityReq
	err := c.BindJSON(&req)
	if err != nil {
		c.IndentedJSON(http.StatusBadRequest, common.GeneralResponse{Success: false, Code: status.Validation, Error: err}.Status(c))
		return
	}

	c.JSON(http.StatusOK, common.GeneralResponse{Success: true})

	go func(callbackUrl string) {
		startTime := time.Now()
		task := &model.CrawlerShipTask{
			Type:         "manual",
			Method:       "crawlRegentSevenSeasActivities",
			Body:         fmt.Sprintf(`{"callbackUrl":"%s"}`, callbackUrl),
			ResponseJson: `{}`,
			StartTime:    &startTime,
			Status:       "running",
		}
		err = model.SaveOrUpdate(task)
		if err != nil {
			logger.Error().Err(err).Msg("Error saving task")
			return
		}

		var results []dto.ShipActivity
		for shipUrlName, shipUrl := range shipUrlMap {
			logger.Info().Str("shipUrlName", shipUrlName).Str("shipUrl", shipUrl).Msg("Crawling activities")
			crawler := regent_seven_seas.NewCrawler(shipUrlName, true, 30*time.Second)
			shipActivities, err := crawler.CrawlShipActivity(shipUrl)
			if err != nil {
				logger.Error().Err(err).Str("shipUrlName", shipUrlName).Msg("Error crawling ship activities")

				callbackData := struct {
					Success bool               `json:"success"`
					Error   string             `json:"error"`
					Results []dto.ShipActivity `json:"results"`
				}{
					Success: false,
					Error:   fmt.Sprintf("Failed to crawl activities for %s: %v", shipUrlName, err),
					Results: results,
				}

				jsonData, jsonErr := json.Marshal(callbackData)
				if jsonErr != nil {
					logger.Error().Err(jsonErr).Msg("Error marshaling callback data")
					return
				}
				endTime := time.Now()
				task.EndTime = &endTime
				task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
				task.ResponseJson = string(jsonData)
				task.Status = "failed"
				task.ErrorMsg = err.Error()
				if err := model.SaveOrUpdate(task); err != nil {
					logger.Error().Err(err).Msg("Error updating task")
				}

				if _, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
					logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
				}
				return
			}

			results = append(results, shipActivities...)

			logger.Info().Int("activityCount", len(shipActivities)).Msgf("Successfully crawled activities for %s", shipUrlName)
		}

		callbackData := struct {
			Success bool               `json:"success"`
			Error   string             `json:"error,omitempty"`
			Results []dto.ShipActivity `json:"results"`
		}{
			Success: true,
			Results: results,
		}

		jsonData, err := json.Marshal(callbackData)
		if err != nil {
			logger.Error().Err(err).Msg("Error marshaling callback data")
			return
		}

		endTime := time.Now()
		task.EndTime = &endTime
		task.Duration = tool.FormatDurationString(endTime.Sub(startTime))
		task.ResponseJson = string(jsonData)
		task.Status = "completed"
		if err := model.SaveOrUpdate(task); err != nil {
			logger.Error().Err(err).Msg("Error updating task")
		}

		resp, err := http.Post(callbackUrl, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			logger.Error().Err(err).Str("callbackUrl", callbackUrl).Msg("Error sending callback")
			return
		}
		defer resp.Body.Close()
	}(req.CallbackUrl)
}
