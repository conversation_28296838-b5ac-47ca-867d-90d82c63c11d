package cache

import (
	"encoding/json"
	"fmt"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/develop"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/status"

	go_platform "bitbucket.org/actechinc/go-platform-golang-sdk"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/redis"
)

func GetSecretIntrospect(Secret string) (*go_platform.TokenClaims, error, string) {
	var authResponse *go_platform.TokenClaims

	r := rdb[redis.SecretIntrospectDB].HGet(c, redis.SecretIntrospect, Secret)
	str, err := r.Result()
	if err != nil {
		goto sync
	}
	err = json.Unmarshal([]byte(str), &authResponse)
	if err != nil {
		goto sync
	}

	// 檢查是否過期
	if time.Now().Unix() >= int64(authResponse.ExpirationTime) {
		goto sync
	}

	return authResponse, nil, ""

sync:
	return ReSyncSecretIntrospect(Secret)
}

func ReSyncSecretIntrospect(Secret string) (*go_platform.TokenClaims, error, string) {
	CleanSecretIntrospectExpired()

	// login
	username := develop.UsernameTest
	if Secret[0:2] == develop.EnvProdPrefix {
		username = develop.UsernameProd
	}
	// Global 模式會在 org=wota 下進行，而 sdk 必須以該 org 的身份下執行
	//platform, err := GetPlatformWithoutApp(user.Organization)
	//platConn := go_platform.NewConnect(platform.PlatClientID, platform.PlatClientSecret, platform.Organization, platform.Application)

	// TODO: 不同環境，cache 中的 key 不同

	tokenRes, err, errCode := go_platform.GetAccessToken(username, Secret)
	if err != nil {
		return nil, fmt.Errorf(getAccessTokenTransform(errCode)), getAccessTokenTransform(errCode)
	}
	authResponse, err, _ := go_platform.TokenIntrospect(tokenRes.AccessToken)
	if err != nil {
		return nil, err, status.AuthorizedTokenInvalid
	}
	str, err := json.Marshal(authResponse)
	if err != nil {
		return nil, err, status.Parse
	}
	rdb[redis.SecretIntrospectDB].HSet(c, redis.SecretIntrospect, Secret, string(str))

	return authResponse, nil, ""
}

func CleanSecretIntrospects() error {
	iter := rdb[redis.SecretIntrospectDB].HScan(c, redis.SecretIntrospect, 0, "*", 0).Iterator()

	var errRes error
	for iter.Next(c) {
		key := iter.Val()
		if err := rdb[redis.SecretIntrospectDB].HDel(c, redis.SecretIntrospect, key).Err(); err != nil {
			errRes = err
		}
	}

	if errRes != nil {
		return errRes
	}
	return errRes
}

func CleanSecretIntrospectExpired() error {
	iter := rdb[redis.SecretIntrospectDB].HScan(c, redis.SecretIntrospect, 0, "*", 0).Iterator()

	var errRes error
	for iter.Next(c) {
		key := iter.Val()

		// 驗證是否過期
		r := rdb[redis.SecretIntrospectDB].HGet(c, redis.SecretIntrospect, key)
		str, err := r.Result()
		if err == nil {
			var authResponse *go_platform.TokenClaims
			err = json.Unmarshal([]byte(str), &authResponse)
			if err == nil && time.Now().Unix() < int64(authResponse.ExpirationTime) {
				continue
			}
		}

		if err := rdb[redis.SecretIntrospectDB].HDel(c, redis.SecretIntrospect, key).Err(); err != nil {
			errRes = err
		}
	}

	if errRes != nil {
		return errRes
	}
	return errRes
}

func getAccessTokenTransform(errCode int) string {
	switch errCode {
	case go_platform.GetAccessTokenErrNoUser:
		return status.AuthorizedAccountNotExist
	case go_platform.GetAccessTokenErrUserOrPassError:
		return status.AuthorizedUserOrPassWrong
	case go_platform.GetAccessTokenErrInvalidPermission:
		return status.PermissionNotAllowService
	case go_platform.GetAccessTokenErrTooManyTimes:
		return status.AuthorizedAccountReqToManyTime
	default:
		return status.CallApi
	}
}
