package cache

import (
	"encoding/json"
	"fmt"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/redis"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/model"
)

// FIXME: wota_be model 臨時解法，待優化
type User struct {
	GormModel
	Organization string    `gorm:"type:varchar(100);uniqueIndex:uk_org_username;index;not null;comment:組織" json:"org"`
	Username     string    `gorm:"type:varchar(100);uniqueIndex:uk_org_username;not null;comment:帳號" json:"username"`
	Firstname    string    `gorm:"type:varchar(100);comment:名字" json:"firstname"`
	Lastname     string    `gorm:"type:varchar(100);comment:姓氏" json:"lastname"`
	Title        string    `gorm:"type:varchar(100);comment:稱謂" json:"title"`
	Lang         string    `gorm:"type:varchar(6);comment:偏好語系" json:"lang"`
	RegisterAt   time.Time `gorm:"comment:註冊時間" json:"registerAt"`
	IsDelete     bool      `gorm:"default:false;comment:標記刪除，保留一段時間後自動刪除" json:"isDelete"`
}

func GetUser(org string, username string) (*User, error) {

	var user *User

	r := rdb[redis.BeUserDB].HGet(c, redis.BeUser, fmt.Sprintf("%s_%s", org, username))
	str, err := r.Result()
	if err != nil {
		goto sync
	}
	err = json.Unmarshal([]byte(str), &user)
	if err != nil {
		goto sync
	}
	return user, nil

sync:
	return ReSyncUser(org, username)
}

func ReSyncUser(org string, username string) (*User, error) {
	CleanUser(org, username)

	var user *User
	result := model.Ctx.DB.Where("organization = ? AND username = ?", org, username).First(&user)
	if result.Error != nil {
		return nil, result.Error
	}

	str, err := json.Marshal(user)
	if err != nil {
		return nil, err
	}
	rdb[redis.BeUserDB].HSet(c, redis.BeUser, fmt.Sprintf("%s_%s", org, username), string(str))

	return user, nil
}

func CleanUser(org string, username string) error {

	var errRes error
	if err := rdb[redis.BeUserDB].HDel(c, redis.BeUser, fmt.Sprintf("%s_%s", org, username)).Err(); err != nil {
		errRes = err
	}

	if errRes != nil {
		return errRes
	}
	return errRes
}

func CleanUsers() error {

	iter := rdb[redis.BeUserDB].HScan(c, redis.BeUser, 0, "*", 0).Iterator()

	var errRes error
	for iter.Next(c) {
		key := iter.Val()
		if err := rdb[redis.BeUserDB].HDel(c, redis.BeUser, key).Err(); err != nil {
			errRes = err
		}
	}

	if errRes != nil {
		return errRes
	}
	return errRes
}

func GetUserId(org string, username string) (uint, error) {

	user, err := GetUser(org, username)
	if err != nil {
		return 0, err
	}
	if user != nil {
		return user.ID, nil
	}

	return 0, fmt.Errorf("cant find user")
}
