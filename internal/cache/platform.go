package cache

import (
	"encoding/json"
	"fmt"
	"time"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/redis"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/model"
	"gorm.io/gorm"
)

func GetPlatforms() (*[]Platform, error) {

	// FIXME: 當 platform 有新的 Org，無法 renew
	var platforms *[]Platform

	r := rdb[redis.PlatformDB].HGet(c, redis.Platform, "all")
	str, err := r.Result()
	if err != nil {
		logger.Error().Msg(err.Error())
		goto sync
	}
	err = json.Unmarshal([]byte(str), &platforms)
	if err != nil {
		logger.Error().Msg(err.Error())
		goto sync
	}
	return platforms, nil

sync:
	return ReSyncPlatforms()
}

func ReSyncPlatforms() (*[]Platform, error) {
	CleanPlatforms()

	var platforms *[]Platform
	result := model.Ctx.DB.Find(&platforms)
	if result.Error != nil {
		logger.Error().Msg(result.Error.Error())
		return nil, result.Error
	}
	if platforms == nil {
		return nil, fmt.Errorf("cant find any platform")
	}

	str, err := json.Marshal(platforms)
	if err != nil {
		return nil, err
	}
	rdb[redis.PlatformDB].HSet(c, redis.Platform, "all", string(str))

	return platforms, nil
}

func CleanPlatforms() error {

	iter := rdb[redis.PlatformDB].HScan(c, redis.Platform, 0, "*", 0).Iterator()

	var errRes error
	for iter.Next(c) {
		key := iter.Val()
		if err := rdb[redis.PlatformDB].HDel(c, redis.Platform, key).Err(); err != nil {
			errRes = err
		}
	}

	if errRes != nil {
		return errRes
	}
	return errRes
}

func GetPlatform(org string, app string) (*Platform, error) {

	platforms, err := GetPlatforms()
	if err != nil {
		return nil, err
	}
	for _, platform := range *platforms {
		if platform.Organization == org && platform.Application == app {
			return &platform, nil
		}
	}

	return nil, fmt.Errorf("not exist")

}

func GetPlatformWithoutApp(org string) (*Platform, error) {

	platforms, err := GetPlatforms()
	if err != nil {
		return nil, err
	}
	for _, platform := range *platforms {
		if platform.Organization == org {
			return &platform, nil
		}
	}

	return nil, fmt.Errorf("org not exist")

}

type GormModel struct {
	ID        uint           `gorm:"primarykey" readonly:"true" json:"id"`
	CreatedAt time.Time      `readonly:"true" json:"createdAt"`
	UpdatedAt time.Time      `readonly:"true" json:"updatedAt"`
	DeletedAt gorm.DeletedAt `gorm:"index" readonly:"true" json:"deletedAt"`
}

type Platform struct {
	GormModel
	PlatClientID           string `gorm:"type:varchar(100);comment:GoPlatform Client ID"`
	PlatClientSecret       string `gorm:"type:varchar(100);comment:GoPlatform Client Secret"`
	Organization           string `gorm:"type:varchar(100);uniqueIndex:uk_platform_app;index;not null;comment:組織"`
	OrgName                string `gorm:"type:varchar(100);comment:組織名稱"`
	OrgShortName           string `gorm:"type:varchar(100);comment:組織縮寫"`
	OrgEnName              string `gorm:"type:varchar(100);comment:組織英文名稱"`
	OrgEnShortName         string `gorm:"type:varchar(100);comment:組織英文縮寫"`
	OrgName2Words          string `gorm:"type:varchar(100);uniqueIndex;not null;column:org_name_2words;comment:組織名稱2碼"`
	OrgContactUserID       uint   `gorm:"comment:組織聯絡人 UserID"`
	OrgContact             string `gorm:"type:varchar(300);comment:組織聯絡人"`
	Application            string `gorm:"type:varchar(100);uniqueIndex:uk_platform_app;not null;comment:應用程式"`
	AppName                string `gorm:"type:varchar(100);comment:應用程式名稱"`
	IsVerifyCreditCard     bool   `gorm:"type:boolean;comment:是否驗證信用卡卡號"`
	CreditCardPrefix       string `gorm:"type:varchar(100);comment:信用卡卡號前綴"`
	CreditCardName         string `gorm:"type:varchar(100);comment:信用卡名稱"`
	IsVerifyEmail          bool   `gorm:"type:boolean;comment:是否驗證 email domain"`
	EmailDomain            string `gorm:"type:varchar(100);comment:email domain"`
	Footer                 string `gorm:"type:varchar(300);comment:頁尾"`
	OrgLogo                string `gorm:"type:varchar(300);comment:Logo"`
	OrgLogoWhite           string `gorm:"type:varchar(300);comment:白底Logo"`
	OrgLogoEmail           string `gorm:"type:varchar(300);comment:LogoEmail"`
	OrgIcon                string `gorm:"type:varchar(300);comment:Icon"`
	OrgFavicon             string `gorm:"type:varchar(300);comment:Favicon"`
	OrgAccountLine         string `gorm:"type:varchar(100);comment:Line帳號"`
	OrgAccountFb           string `gorm:"type:varchar(100);comment:FB帳號"`
	OrgPhone               string `gorm:"type:varchar(100);comment:電話"`
	EmailSubject           string `gorm:"type:varchar(100);comment:信件主標題"`
	EmailSender            string `gorm:"type:varchar(100);comment:信件發送人"`
	OrgMetaTitle           string `gorm:"type:varchar(100);comment:meta標題"`
	OrgMetaKeyword         string `gorm:"type:varchar(300);comment:meta關鍵字"`
	OrgMetaDescription     string `gorm:"type:varchar(300);comment:meta描述"`
	OrgPrivilegeRatePrefix string `gorm:"type:varchar(50);comment:禮遇方案的名稱前綴"`
	AppFeDomain            string `gorm:"type:varchar(300);comment:前端網域"`
	IsFastTrack            bool   `gorm:"type:boolean;default:false;comment:是否啟用通關禮遇"`
	IsDevelop              bool   `gorm:"type:boolean;default:false;comment:是否使用開發功能"`
	DevelopApiSecretTest   string `gorm:"type:varchar(300);comment:API金鑰(測試)"`
	DevelopApiSecretProd   string `gorm:"type:varchar(300);comment:API金鑰"`
	DevelopApiShowProd     bool   `gorm:"type:boolean;default:false;comment:顯示正式 API Secret"`
	// OrgMetaIconFilename string `gorm:"type:varchar(100);comment:meta icon url"`
	// OrgMetaSiteName     string `gorm:"type:varchar(100);comment:meta網站名稱"`
	// OrgMetaUrl          string `gorm:"type:varchar(300);comment:meta網址"`
	// OrgMetaImage        string `gorm:"type:varchar(300);comment:meta圖片"`
}
