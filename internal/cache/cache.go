package cache

import (
	"context"

	"github.com/go-redis/redis/v8"

	zlog "bitbucket.org/actechinc/golang_tools/logger"
	"bitbucket.org/actechinc/wota_ship_crawler/conf"
)

var c = context.Background()
var config = conf.Config
var logger = zlog.NewBaseLogger(config.ENV)

var rdb = []*redis.Client{
	redis.NewClient(&redis.Options{
		Addr:     config.RedisHost,
		Password: "", // no password set
		DB:       0,  // use default DB
	}),
	redis.NewClient(&redis.Options{
		Addr:     config.RedisHost,
		Password: "", // no password set
		DB:       1,  // use default DB
	}),
	redis.NewClient(&redis.Options{
		Addr:     config.RedisHost,
		Password: "", // no password set
		DB:       2,  // use default DB
	}),
	redis.NewClient(&redis.Options{
		Addr:     config.RedisHost,
		Password: "", // no password set
		DB:       3,  // use default DB
	}),
	redis.NewClient(&redis.Options{
		Addr:     config.RedisHost,
		Password: "", // no password set
		DB:       4,  // use default DB
	}),
	redis.NewClient(&redis.Options{
		Addr:     config.RedisHost,
		Password: "", // no password set
		DB:       5,  // use default DB
	}),
}
