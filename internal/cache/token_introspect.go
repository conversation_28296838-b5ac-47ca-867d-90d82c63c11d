package cache

import (
	"encoding/json"
	"time"

	go_platform "bitbucket.org/actechinc/go-platform-golang-sdk"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/constant/redis"
)

func GetTokenIntrospect(token string) (*go_platform.TokenClaims, error) {
	var authResponse *go_platform.TokenClaims

	r := rdb[redis.TokenIntrospectDB].HGet(c, redis.TokenIntrospect, token)
	str, err := r.Result()
	if err != nil {
		goto sync
	}
	err = json.Unmarshal([]byte(str), &authResponse)
	if err != nil {
		goto sync
	}

	// 檢查是否過期
	if time.Now().Unix() >= int64(authResponse.ExpirationTime) {
		goto sync
	}

	return authResponse, nil

sync:
	return ReSyncTokenIntrospect(token)
}

func ReSyncTokenIntrospect(token string) (*go_platform.TokenClaims, error) {
	CleanTokenIntrospectExpired()

	authResponse, err, _ := go_platform.TokenIntrospect(token)
	if err != nil {
		return nil, err
	}
	str, err := json.Marshal(authResponse)
	if err != nil {
		return nil, err
	}
	rdb[redis.TokenIntrospectDB].HSet(c, redis.TokenIntrospect, token, string(str))

	return authResponse, nil
}

func CleanTokenIntrospects() error {
	iter := rdb[redis.TokenIntrospectDB].HScan(c, redis.TokenIntrospect, 0, "*", 0).Iterator()

	var errRes error
	for iter.Next(c) {
		key := iter.Val()
		if err := rdb[redis.TokenIntrospectDB].HDel(c, redis.TokenIntrospect, key).Err(); err != nil {
			errRes = err
		}
	}

	if errRes != nil {
		return errRes
	}
	return errRes
}

func CleanTokenIntrospectExpired() error {
	iter := rdb[redis.TokenIntrospectDB].HScan(c, redis.TokenIntrospect, 0, "*", 0).Iterator()

	var errRes error
	for iter.Next(c) {
		key := iter.Val()

		// 驗證是否過期
		r := rdb[redis.TokenIntrospectDB].HGet(c, redis.TokenIntrospect, key)
		str, err := r.Result()
		if err == nil {
			var authResponse *go_platform.TokenClaims
			err = json.Unmarshal([]byte(str), &authResponse)
			if err == nil && time.Now().Unix() < int64(authResponse.ExpirationTime) {
				continue
			}
		}

		if err := rdb[redis.TokenIntrospectDB].HDel(c, redis.TokenIntrospect, key).Err(); err != nil {
			errRes = err
		}
	}

	if errRes != nil {
		return errRes
	}
	return errRes
}
