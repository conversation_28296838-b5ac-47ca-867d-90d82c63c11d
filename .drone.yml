kind: pipeline
type: docker
name: Test

steps:
  - name: golangci-lint
    image: golangci/golangci-lint:v1.31.0-alpine
    # commands:
#      - golangci-lint run

---
kind: pipeline
type: docker
name: Build

steps:
  - name: "notify - push"
    image: plugins/slack
    settings:
      room: general
      webhook:
        from_secret: AWS_DRONECI_SLACK_WEBHOOK
      username: Drone CI
      title:
        from_secret: AWS_FE_MAIN_ORG
      fallback:
        from_secret: AWS_DRONECI_CI_URL
      template: >
        Drone: `{{ config.title }}` / <{{ build.link }}| Build #{{ build.number }} >

        Commit: <{{ build.link }}| {{ repo.name }} > / <{{ config.fallback }}/link/{{ repo.owner }}/{{ repo.name }}/tree/{{ build.ref }}|{{ build.branch }}> / <{{ config.fallback }}/link/{{ repo.owner }}/{{ repo.name }}/commit/{{ build.commit }}|{{ truncate build.commit 8 }}>
    when:
      event:
        - push

  - name: "notify - build"
    image: plugins/slack
    settings:
      room: general
      webhook:
        from_secret: AWS_DRONECI_SLACK_WEBHOOK
      username: Drone CI
      template: Running...🏃🏃🏃
    when:
      event:
        - push
      branch:
        - master

  - name: build
    image: plugins/ecr
    environment:
      CMD_NAME: wota_ship_crawler
      USE_SSM: true
      SSM_ACCOUNT:
        from_secret: AWS_DRONECI_SSM_IAM_ID
      SSM_SECRET:
        from_secret: AWS_DRONECI_SSM_IAM_SECRET
      SSM_REGION:
        from_secret: AWS_DRONECI_SSM_REGION
      SSM_ID:
        from_secret: AWS_DRONECI_SSM_ID
      SSH_PRIVATE_KEY:
        from_secret: bitbucket_private_key
      BUILD_VERSION: ${DRONE_BUILD_NUMBER}
      COMMIT_ID: ${DRONE_COMMIT}
    settings:
      build_args_from_env:
        - CMD_NAME
        - USE_SSM
        - SSM_ACCOUNT
        - SSM_SECRET
        - SSM_REGION
        - SSM_ID
        - SSH_PRIVATE_KEY
        - BUILD_VERSION
        - COMMIT_ID
      access_key:
        from_secret: AWS_DRONECI_ECR_IAM_ID
      secret_key:
        from_secret: AWS_DRONECI_ECR_IAM_SECRET
      repo: wota_ship_crawler-master
      registry: ${AWS_DRONECI_MAIN_REGISTRY}
      region: ap-northeast-1
      dockerfile: ./Dockerfile
      tags:
        - v${DRONE_BUILD_NUMBER}
        - latest
    when:
      event:
        - push
      branch:
        - master

  - name: "deploy to moon"
    image: appleboy/drone-ssh:1.6.4
    environment:
      FOLDER:
        from_secret: AWS_DRONECI_DEV_MAIN_FOLDER
    settings:
      host:
        from_secret: AWS_DEV_SSH_HOST
      port:
        from_secret: AWS_DEV_SSH_PORT
      username:
        from_secret: AWS_DEV_SSH_USER
      password:
        from_secret: AWS_DEV_SSH_PW
      envs:
        - FOLDER
      script:
        - date > last_deploy_server_date.txt
        - cd $FOLDER
        - sudo sh login.sh
        - sudo sh run.sh
    when:
      event:
        - push
      branch:
        - master

  - name: "notify - finish"
    image: plugins/slack
    settings:
      room: general
      webhook:
        from_secret: AWS_DRONECI_SLACK_WEBHOOK
      username: Drone CI
      title:
        from_secret: AWS_FE_MAIN_ORG
      fallback:
        from_secret: AWS_DRONECI_DOMAIN
      template: >
        Drone: `{{ config.title }}` / <{{ build.link }}| Build #{{ build.number }} > {{#success build.status}} ✅ {{ else }} ❌ {{/success}}

        Url: <https://dev-api.{{ config.fallback }}/swagger/media/index.html>
        
        Url: <https://dev-api-dbs.wotaluxe.com/swagger/media/index.html>

        Url: <https://dev-api-demo.wotaluxe.com/swagger/media/index.html>
    when:
      status:
        - success
        - failure
      event:
        - push
      branch:
        - master

---
kind: secret
name: AWS_DRONECI_MAIN_REGISTRY
get:
  path: wota
  name: DRONECI_MAIN_REGISTRY

---
kind: secret
name: AWS_DRONECI_DEV_MAIN_FOLDER
get:
  path: wota
  name: DRONECI_DEV_MAIN_FOLDER

---
kind: secret
name: AWS_DRONECI_ECR_IAM_ID
get:
  path: wota
  name: DRONECI_ECR_IAM_ID

---
kind: secret
name: AWS_DRONECI_ECR_IAM_SECRET
get:
  path: wota
  name: DRONECI_ECR_IAM_SECRET

---
kind: secret
name: AWS_DRONECI_SLACK_WEBHOOK
get:
  path: wota
  name: DRONECI_SLACK_WEBHOOK

---
kind: secret
name: AWS_DRONECI_SSM_IAM_ID
get:
  path: wota
  name: DRONECI_SSM_IAM_ID

---
kind: secret
name: AWS_DRONECI_SSM_IAM_SECRET
get:
  path: wota
  name: DRONECI_SSM_IAM_SECRET

---
kind: secret
name: AWS_DRONECI_SSM_REGION
get:
  path: wota
  name: DRONECI_SSM_REGION

---
kind: secret
name: AWS_DRONECI_SSM_ID
get:
  path: wota
  name: DRONECI_SSM_ID

---
kind: secret
name: AWS_DRONECI_DOMAIN
get:
  path: wota
  name: DRONECI_DOMAIN

---
kind: secret
name: AWS_DRONECI_CI_URL
get:
  path: wota
  name: DRONECI_CI_URL

---
kind: secret
name: AWS_FE_MAIN_ORG
get:
  path: wota
  name: FE_MAIN_ORG

---
kind: secret
name: AWS_DEV_SSH_HOST
get:
  path: wota
  name: DEV_SSH_HOST

---
kind: secret
name: AWS_DEV_SSH_PORT
get:
  path: wota
  name: DEV_SSH_PORT

---
kind: secret
name: AWS_DEV_SSH_USER
get:
  path: wota
  name: DEV_SSH_USER

---
kind: secret
name: AWS_DEV_SSH_PW
get:
  path: wota
  name: DEV_SSH_PW

---
kind: secret
name: AWS_UAT_SSH_HOST
get:
  path: wota
  name: UAT_SSH_HOST

---
kind: secret
name: AWS_UAT_SSH_PORT
get:
  path: wota
  name: UAT_SSH_PORT

---
kind: secret
name: AWS_UAT_SSH_USER
get:
  path: wota
  name: UAT_SSH_USER

---
kind: secret
name: AWS_UAT_SSH_PW
get:
  path: wota
  name: UAT_SSH_PW

---
kind: secret
name: AWS_PROD_SSH_HOST
get:
  path: wota
  name: PROD_SSH_HOST

---
kind: secret
name: AWS_PROD_SSH_PORT
get:
  path: wota
  name: PROD_SSH_PORT

---
kind: secret
name: AWS_PROD_SSH_USER
get:
  path: wota
  name: PROD_SSH_USER

---
kind: secret
name: AWS_PROD_SSH_PW
get:
  path: wota
  name: PROD_SSH_PW
