package conf

import (
	"fmt"
	"os"
	"strings"
	"sync"

	"github.com/mcuadros/go-defaults"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/ssm"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/tool"
	"github.com/rs/zerolog/log"
	"github.com/spf13/viper"
)

// .env 可以通用於 docker-compose or k8s
// 參數名稱，參考以下範例：
//
//	   Version         string
//	     => VERSION
//	   SwaggerFilePath string `mapstructure:"swagger_file_path"`
//		=> SWAGGER_FILE_PATH
type config struct {
	ENV              string `default:"DEV"`
	Debug            bool   `mapstructure:"debug" default:"false"`
	Version          string
	ConfigDebug      bool   `mapstructure:"config_debug" default:"false"`
	BuildVersion     string `mapstructure:"build_version"`
	CommitId         string `mapstructure:"commit_id"`
	Port             string `default:"8080"`
	Timezone         string `mapstructure:"tz"`
	MainOrg          string `mapstructure:"main_org"`
	SwaggerFilePath  string `mapstructure:"swagger_file_path"`
	UseSsm           bool   `mapstructure:"use_ssm" default:"false"`
	SsmInUse         bool   `default:"false"`
	SsmAccount       string `mapstructure:"ssm_account"`
	SsmSecret        string `mapstructure:"ssm_secret"`
	SsmRegion        string `mapstructure:"ssm_region" default:"ap-northeast-1"`
	SsmId            string `mapstructure:"ssm_id"`
	S3AccessID       string `mapstructure:"s3_access_id"`
	S3AccessKey      string `mapstructure:"s3_access_key"`
	S3Region         string `mapstructure:"s3_region"`
	S3Bucket         string `mapstructure:"s3_bucket"`
	S3BucketDomain   string `mapstructure:"s3_bucket_domain"`
	S3MediaPath      string `mapstructure:"s3_media_path" default:"upload"`
	BeDsn            string `mapstructure:"be_dsn"`
	RedisHost        string `mapstructure:"redis_host"`
	FluentBitHost    string `mapstructure:"fluent_bit_host"`
	FluentBitPort    int    `mapstructure:"fluent_bit_port"`
	SqlAutoMigrate   bool   `mapstructure:"sql_auto_migrate"`
	SqlDebug         bool   `mapstructure:"sql_debug"`
	PlatEndpoint     string `mapstructure:"plat_endpoint"`
	PlatClientId     string `mapstructure:"plat_client_id"`
	PlatClientSecret string `mapstructure:"plat_client_secret"`
	PlatOrgName      string `mapstructure:"plat_org_name"`
	PlatAppName      string `mapstructure:"plat_app_name"`
	NATsURL          string `mapstructure:"nats_url" default:"nats://127.0.0.1:4222"`
	SlackUrlLog      string `mapstructure:"slack_url_log"`
	OpenaiApiKey     string `mapstructure:"openai_api_key"`
}

var Config = &config{}

func init() {
	once := sync.Once{}

	// config priority: os > ssm > file > default
	once.Do(func() {

		//套用預設值
		Config = new(config)
		defaults.SetDefaults(Config)

		// 有設定檔就使用，沒有則忽略 (通常只在 local
		// 注意！如果 .env 有 宣告參數名稱沒給值，將會覆蓋掉 default 值，ex. NATS_URL=
		viper.AddConfigPath(".")
		viper.AddConfigPath("./conf/")
		viper.AddConfigPath("../conf/")
		viper.AddConfigPath("../../conf/")
		viper.AddConfigPath("../../../conf/")
		//absDir, _ := filepath.Abs(filepath.Dir(os.Args[0]))
		//viper.AddConfigPath(absDir + "./conf/")
		viper.SetConfigName("config")
		if err := viper.ReadInConfig(); err != nil {
			if Config.ConfigDebug {
				log.Info().Msg("config file not exist" + err.Error())
			}
		}

		// 使用 OS 環境參數覆蓋 config
		viper.AutomaticEnv()
		for _, row := range os.Environ() {
			arr := strings.Split(row, "=")
			viper.BindEnv(arr[0]) // viper 需綁定才會在 Unmarshal 中生效，只能辨別「全大寫 mapstructure」或「全大寫 struct」
		}

		// config object 賦值
		if err := viper.Unmarshal(&Config); err != nil {
			panic(fmt.Errorf("Fatal Unmarshal config: %w \n", err))
		}

		// 監控設定檔變化
		//viper.OnConfigChange(func(e fsnotify.Event) {
		//	fmt.Println("Config file changed:", e.Name)
		//})
		//viper.WatchConfig()

		// 使用 ssm
		if Config.UseSsm {
			// 環境切分有兩種方式
			// 1. 用不同 SsmId 區分 (標準
			// 2. 同 SsmId，欄位用 prefix 區分 (省錢
			secret, err := ssm.GetAWSSecrets(Config, Config.SsmAccount, Config.SsmSecret, Config.SsmRegion, Config.SsmId, strings.ToUpper(Config.ENV)+"_")
			if err != nil {
				log.Error().Msg("get ssm failed:" + err.Error())
			}

			// os env 的 priority 最高，需再次覆蓋
			for _, row := range os.Environ() {
				arr := strings.Split(row, "=")
				viper.BindEnv(arr[0]) // viper 需綁定才會在 Unmarshal 中生效，只能辨別「全大寫 mapstructure」或「全大寫 struct」
			}
			if err := viper.Unmarshal(&Config); err != nil {
				panic(fmt.Errorf("Fatal Unmarshal config again: %w \n", err))
			}

			if secret != nil {
				Config.SsmInUse = true
			}
		}

		if Config.ConfigDebug {
			fmt.Println(tool.ToJson(Config))
		}
	})
}
