# ENV KeyName is mapstructure uppercase or struct name uppercase
# app
ENV=DEV
#VERSION=
#CONFIG_DEBUG=
#BUILD_VERSION=
#COMMITID=

# service
PORT=8085
TZ=Asia/Taipei

# swagger
SWAGGER_FILE_PATH=./docs/swagger.json

# AWS secret (close when develop, save your money)
USE_SSM=true
#SSM_ACCOUNT=
#SSM_SECRET=
#SSM_REGION=
#SSM_ID=

# DB
BE_DSN=root:test@tcp(db_arm:3309)/wota_be?charset=utf8mb4&parseTime=True
#SQL_AUTO_MIGRATE=
#SQL_DEBUG=

# Redis
REDIS_HOST=localhost:6379

# Nats
#NATS_URL=

# Fluent-bit
FLUENT_BIT_HOST=localhost
FLUENT_BIT_PORT=24222

# Go-Platform
#PLAT_ENDPOINT=
#PLAT_CLIENT_ID=
#PLAT_CLIENT_SECRET=
#PLAT_ORG_NAME=
#PLAT_APP_NAME=
#PLAT_ORG_NAME_CLIENT_ID=
#APP_FE_DOMAIN=http://abc.com/
