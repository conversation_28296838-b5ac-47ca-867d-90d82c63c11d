# FROM ubuntu
FROM golang:alpine3.20 AS builder
ARG CMD_NAME
ARG SSH_PRIVATE_KEY
RUN apk add git; \
    apk add make; \
    apk add openssh; \
    apk add openssh-client; \
    apk add openrc; \
    apk add zip; \
    apk add tzdata;
RUN zip -r -0 /zoneinfo.zip /usr/share/zoneinfo

ENV CMD_NAME=${CMD_NAME}
ENV SSH_PRIVATE_KEY=${SSH_PRIVATE_KEY}

RUN mkdir -p /root/.ssh && \
    echo "$SSH_PRIVATE_KEY" > /root/.ssh/id_rsa && \
    chmod 600 /root/.ssh/id_rsa && \
    ssh-keyscan bitbucket.org > /root/.ssh/known_hosts

RUN git config --global url."*****************:".insteadOf "https://bitbucket.org/"
ENV GOPRIVATE=bitbucket.org/actechinc
RUN /etc/init.d/sshd restart; exit 0

# Set necessary environmet variables needed for our image
ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64

# Move to working directory /build
ENV WORK_HOME="app"
ENV CMD_NAME=${CMD_NAME}
WORKDIR /$WORK_HOME

# Copy the code into the container
COPY . .

# Build the application
RUN go install github.com/swaggo/swag/cmd/swag@v1.8.1
RUN make init_for_docker
RUN make swag || true
RUN make build_$CMD_NAME

# # Move to /dist directory as the place for resulting binary folder
WORKDIR /dist

# Copy binary from build to stock folder
RUN cp /$WORK_HOME/build/$CMD_NAME main

RUN cp -a /$WORK_HOME/docs .


# # Build a small image ===================================
# FROM scratch  因為需要安裝 ffmpeg 跟 yt-dlp 所以沒辦法繼續使用 scratch
FROM alpine:3.20

# Install yt-dlp and its dependencies
RUN apk add --no-cache python3 py3-pip ffmpeg && \
    pip3 install --no-cache-dir --break-system-packages yt-dlp

# Install Ghostscript for PDF to image conversion
RUN apk add --no-cache ghostscript

# Install Chrome dependencies for headless browser
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    nodejs \
    yarn \
    xvfb \
    dbus \
    fontconfig

# Set environment variables for Chrome
ENV CHROME_BIN=/usr/bin/chromium-browser \
    CHROME_PATH=/usr/lib/chromium/ \
    PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /dist /

ENV ZONEINFO /zoneinfo.zip
COPY --from=builder /zoneinfo.zip /

#For DroneCI
ARG ENV
ARG USE_SSM
ARG SSM_ACCOUNT
ARG SSM_SECRET
ARG SSM_REGION
ARG SSM_ID
ARG BUILD_VERSION
ARG COMMIT_ID

#DroneCI to to program env
ENV ENV="${ENV}"
ENV USE_SSM="${USE_SSM}"
ENV SSM_ACCOUNT="${SSM_ACCOUNT}"
ENV SSM_SECRET="${SSM_SECRET}"
ENV SSM_REGION="${SSM_REGION}"
ENV SSM_ID="${SSM_ID}"
ENV BUILD_VERSION="${BUILD_VERSION}"
ENV COMMIT_ID="${COMMIT_ID}"


# # Command to run
ENTRYPOINT ["/main"]