package main

import (
	"fmt"

	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/swagger"

	zlog "bitbucket.org/actechinc/golang_tools/logger"
	"bitbucket.org/actechinc/wota_ship_crawler/conf"
	"bitbucket.org/actechinc/wota_ship_crawler/docs"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/model"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/pkg/loki"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/router"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/schedule"
	"bitbucket.org/actechinc/wota_ship_crawler/internal/validator"
)

var (
	config = conf.Config
	logger = zlog.NewBaseLogger(config.ENV)
)

// @title           Wota Ship Crawler API Service
// @version         0.0.1
// @BasePath  /
// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
func main() {
	if err := run(); err != nil {
		logger.Error().Msg(err.Error())
	}
}

func run() error {
	model.Ctx.InitWithDSN(config.BeDsn)

	if config.MainOrg == "" {
		return fmt.Errorf("config lose main_org")
	}

	schedule.Admin()

	api, err := router.SetupRouter()
	if err != nil {
		return fmt.Errorf("error happened in setup router: %w", err)
	}

	swagger.InitSwagger(api, config.SwaggerFilePath, docs.SwaggerInfo)
	loki.InitFluentBit(config.FluentBitHost, config.FluentBitPort, config.ENV)
	validator.InitValidator()

	api.Run(":" + config.Port)

	return nil
}
