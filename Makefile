init_for_docker:
	export GOPRIVATE="bitbucket.org/actechinc"
	git config --global url."*****************:".insteadOf "https://bitbucket.org/"
	#go mod tidy

init:
	touch go.mod
	touch go.sum
	rm go.mod
	rm go.sum
	go mod init bitbucket.org/actechinc/wota_ship_crawler
	export GOPRIVATE="bitbucket.org/actechinc"
	git config --global url."*****************:".insteadOf "https://bitbucket.org/"
	go mod tidy

build: build_wota_ship_crawler

build_wota_ship_crawler:
	go build -o build/wota_ship_crawler cmd/wota_ship_crawler/*.go

run:
	go run cmd/wota_ship_crawler/*.go

clean:
	go clean
	rm build/*

swag:
	swag init -g cmd/wota_ship_crawler/main.go -o docs --parseDependency --parseInternal

# 因為分兩台 drone server，所以需要分開部署
# 在 mac 環境中需分別設定 
# wota: DRONE_SERVER_WOTA 和 DRONE_TOKEN_WOTA
# bdt: DRONE_SERVER_BDT 和 DRONE_TOKEN_BDT
deploy:
	MAIN_ORG=$(shell read -p "組織(wota / bdt) : " o ; echo $${o} | tr '[:lower:]' '[:upper:]');\
	BUILD=$(shell read -p "近期 Build Number : #" n ; echo $${n});\
	TAG=$(shell read -p "正式 Tag 版號 : v" t ; echo $${t});\
	BRANCH=$(shell read -p "要部署到 (uat / prod) : " e ; echo $${e});\
	SERVER_VAR=DRONE_SERVER_$${MAIN_ORG};\
	TOKEN_VAR=DRONE_TOKEN_$${MAIN_ORG};\
	DRONE_SERVER=$${!SERVER_VAR} DRONE_TOKEN=$${!TOKEN_VAR} drone build promote actechinc/wota_ship_crawler "$$BUILD" "$$BRANCH" --param=tag="v$$TAG"
	#需先登入： "http://drone.alphacore.cc/account"
	#查詢近期版本號： $(drone build ls --status success actechinc/wota_be |grep "Build #" | head -1 | awk '{print $2}')|sed "s/#//g";\

.PHONY: build