# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Prompt Optimization
Before executing any instruction, Claude <PERSON> should first read the `prompt_optimizer.md` file and follow its guidance to optimize every prompt. After optimizing the prompt according to the guidelines in that file, execute the instruction based on the optimized prompt.
If the optimizer does not have sufficient information to generate an effective prompt, it should ask clarifying questions to the user to ensure the information is complete before proceeding.

## Development Principles
When working with this codebase, please follow these principles:
- For any uncertain or ambiguous requirements, always confirm with me or ask clarifying questions.
- For complex requirements, first create a plan and confirm with me before taking actual implementation actions.
- Do not make unauthorized changes outside my explicit instructions, unless they have a clear relationship to the current requirement.
- If the project has existing content, reference the existing style and maintain consistency throughout the project.
- When there is no reference for programming style, prioritize using official guidelines or community conventions.
- Pay attention to using linters for formatting on all modifications.
- Prohibited from adding, deleting, or modifying data beyond the project scope; only reading is allowed.
- When the LLM foundation model's original training data is uncertain or potentially outdated, search the web before proceeding.
- Before completing operations, run `go fmt` and other corresponding language formatters to organize formatting, and use linters to check for errors.

## Common Development Commands

### Build and Run
```bash
# Initialize Go modules with private repository access
make init

# Build the binary
make build

# Run the application
make run

# Clean build artifacts
make clean

# Generate Swagger documentation
make swag

# Deploy using Drone CI (interactive)
make deploy
```

### Testing
```bash
# Run tests for specific service (organized by cruise line brand)
# Regent Seven Seas services
go test ./internal/service/regent_seven_seas/cruises_test.go
go test ./internal/service/regent_seven_seas/itinerary_test.go
go test ./internal/service/regent_seven_seas/activity_test.go
go test ./internal/service/regent_seven_seas/deck_image_test.go
go test ./internal/service/regent_seven_seas/spec_test.go
go test ./internal/service/regent_seven_seas/suites_test.go
go test ./internal/service/regent_seven_seas/video_test.go
go test ./internal/service/regent_seven_seas/itinerary_map_view_test.go

# Silver Seas services
go test ./internal/service/silver_seas/cruises_test.go
go test ./internal/service/silver_seas/activity_test.go
go test ./internal/service/silver_seas/deck_image_test.go
go test ./internal/service/silver_seas/spec_test.go
go test ./internal/service/silver_seas/suites_test.go
go test ./internal/service/silver_seas/cover_media_test.go

# Run all tests in a service directory
go test ./internal/service/regent_seven_seas/...
go test ./internal/service/silver_seas/...

# Pattern: go test ./internal/service/<cruise_line>/<service_name>_test.go

# Tests can now be run directly from IDE or command line without additional setup
# The config.go has been updated to look for config.env in the correct path
```

## High-Level Architecture

This is a Go microservice for crawling cruise ship information from multiple cruise lines including:
- Regent Seven Seas Cruises (https://www.rssc.com/)
- Silver Seas Cruises (https://www.silversea.com/)

The service uses web scraping to extract ship specifications, itineraries, suite information, activities, and media content.

### Key Architectural Patterns

1. **Clean Architecture Layout**:
   - Controllers (`/internal/controller`) handle HTTP requests and call services
     - `crawler.go` - Regent Seven Seas crawler endpoints
     - `silver_seas_crawler.go` - Silver Seas crawler endpoints
   - Services organized by cruise line brand:
     - `/internal/service/regent_seven_seas/` - Regent Seven Seas crawling logic
     - `/internal/service/silver_seas/` - Silver Seas crawling logic
   - DTOs (`/internal/dto`) define request/response structures
   - Public DTOs (`/pkg/wota_ship_crawler/dto`) provide external interfaces for other services
   - Models (`/internal/model`) define database entities
   - Each crawler type has its own service file and corresponding test file

2. **Asynchronous Processing Pattern**:
   - All crawler endpoints return immediate 200 response
   - Processing happens in goroutines
   - Task tracking via `CrawlerShipTask` model in database
   - Callback URLs notify completion with results
   - Error handling includes retry logic and detailed logging

3. **Web Scraping Implementation**:
   - Uses go-rod (Chrome automation) for scraping
   - Services are organized by cruise line:
     - `/internal/service/regent_seven_seas/` - Regent Seven Seas scrapers
     - `/internal/service/silver_seas/` - Silver Seas scrapers
   - Each service file follows naming pattern: `<feature>.go` (e.g., `activity.go`, `cruises.go`)
   - Services interact with external websites, extract data, and store in database
   - Media files are uploaded to S3
   - Browser instances are managed per crawl to avoid conflicts
   - Implements retry logic and HTTP status code checking for reliability
   - Ship URL mappings defined in respective controller files

4. **Middleware Stack** (order matters):
   - CORS → Language → Platform → Access Log → Parse Token → Auth Required → Permission

5. **Configuration**:
   - Environment variables loaded from: OS env → AWS SSM → config file → defaults
   - Key configs: MySQL connection (`BE_DSN`), Redis (`REDIS_HOST`), AWS services
   - Environment specified by `ENV` variable (DEV/UAT/PROD)
   - Local development uses different browser paths and behaviors

6. **Dependencies** (Go 1.24.3):
   - Web framework: Gin v1.10.1
   - ORM: GORM v1.26.1 with MySQL driver
   - Web scraping: go-rod v0.116.2
   - Caching: Redis v8.11.5
   - Cloud: AWS SDK v1.55.7 (S3, SSM)
   - Internal packages from `bitbucket.org/actechinc/`
   - Logging: Zerolog v1.34.0 with Loki integration
   - Scheduling: robfig/cron v3.0.1
   - Validation: go-playground/validator v10.26.0
   - API Documentation: Swagger (swaggo)

### Service Development Pattern

When adding new crawler functionality:

**For Regent Seven Seas:**
1. Create service in `/internal/service/regent_seven_seas/<feature>.go`
2. Create corresponding test file `<feature>_test.go`
3. Define DTO in `/internal/dto/`
4. Add controller endpoint in `/internal/controller/crawler.go`
5. Register route in `/internal/router/router.go`
6. Run `make swag` to update API documentation

**For Silver Seas:**
1. Create service in `/internal/service/silver_seas/<feature>.go`
2. Create corresponding test file `<feature>_test.go`
3. Define DTO in `/internal/dto/` (with `SilverSeas` prefix)
4. Add controller endpoint in `/internal/controller/silver_seas_crawler.go`
5. Register route in `/internal/router/router.go`
6. Run `make swag` to update API documentation

### Available Crawler Services

**Regent Seven Seas Cruises (https://www.rssc.com/):**
- **Ship Specifications**: Crawls technical specs and features
- **Deck Images**: Downloads deck plan images
- **Ship Videos**: Extracts and processes ship videos
- **Suites**: Detailed suite/cabin information
- **Cruises**: Available cruise itineraries
- **Itinerary Details**: Day-by-day port information
- **Map Views**: Interactive itinerary maps
- **Activities**: Ship activities and amenities
- **Cover Media**: Ship cover images and videos

**Silver Seas Cruises (https://www.silversea.com/):**
- **Ship Specifications**: Crawls technical specs and features
- **Deck Images**: Downloads deck plan images
- **Cover Media**: Ship cover images and videos
- **Suites**: Detailed suite/cabin information
- **Activities**: Ship activities and amenities
- **Cruises**: Available cruise itineraries (under development)

**Supported Ships:**
- **Regent Seven Seas**: Navigator, Explorer, Mariner, Voyager, Seven Seas Grandeur, Seven Seas Splendor
- **Silver Seas**: Dawn, Muse, Moon, Nova, Ray, Spirit, Whisper, Wind, Shadow, Explorer, Wind Expedition, Origin